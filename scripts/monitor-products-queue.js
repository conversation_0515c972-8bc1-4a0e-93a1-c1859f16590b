#!/usr/bin/env node

/**
 * Script para monitorear el progreso de la cola de productos
 * Uso: node scripts/monitor-products-queue.js [jobId]
 */

const BASE_URL = process.env.NUXT_SITE_URL || 'http://localhost:3000'

async function fetchJobStatus(jobId) {
    try {
        const response = await fetch(`${BASE_URL}/api/enyes/system/products-queue-status?jobId=${jobId}`)
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }
        return await response.json()
    } catch (error) {
        console.error(`❌ Error fetching job status: ${error.message}`)
        return null
    }
}

async function fetchQueueStatus() {
    try {
        const response = await fetch(`${BASE_URL}/api/enyes/system/products-queue-status`)
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }
        return await response.json()
    } catch (error) {
        console.error(`❌ Error fetching queue status: ${error.message}`)
        return null
    }
}

async function startTestJob() {
    try {
        console.log('🧪 Starting test job...')
        const response = await fetch(`${BASE_URL}/api/enyes/system/test-products-queue`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }
        
        const result = await response.json()
        console.log(`✅ Test job started with ID: ${result.jobId}`)
        return result.jobId
    } catch (error) {
        console.error(`❌ Error starting test job: ${error.message}`)
        return null
    }
}

function formatProgress(progress) {
    if (typeof progress === 'object' && progress.percentage !== undefined) {
        const elapsed = progress.elapsed ? Math.floor(progress.elapsed / 1000) : 0
        return `${progress.percentage}% - ${progress.message} (${elapsed}s)`
    }
    if (typeof progress === 'number') {
        return `${progress}%`
    }
    return 'No progress info'
}

function formatDuration(ms) {
    const seconds = Math.floor(ms / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    
    if (hours > 0) {
        return `${hours}h ${minutes % 60}m ${seconds % 60}s`
    } else if (minutes > 0) {
        return `${minutes}m ${seconds % 60}s`
    } else {
        return `${seconds}s`
    }
}

async function monitorJob(jobId) {
    console.log(`\n🔍 Monitoring job: ${jobId}`)
    console.log('Press Ctrl+C to stop monitoring\n')
    
    let lastProgress = -1
    let startTime = Date.now()
    
    const monitor = setInterval(async () => {
        const status = await fetchJobStatus(jobId)
        if (!status) return
        
        const currentTime = Date.now()
        const elapsed = formatDuration(currentTime - startTime)
        
        // Clear previous line and show current status
        process.stdout.write('\r\x1b[K') // Clear line
        
        let progressText = formatProgress(status.progress)
        let stateIcon = '⏳'
        
        switch (status.state) {
            case 'waiting': stateIcon = '⏳'; break
            case 'active': stateIcon = '🔄'; break
            case 'completed': stateIcon = '✅'; break
            case 'failed': stateIcon = '❌'; break
            case 'delayed': stateIcon = '⏸️'; break
        }
        
        process.stdout.write(`${stateIcon} [${elapsed}] ${status.state.toUpperCase()} - ${progressText}`)
        
        if (status.state === 'completed') {
            console.log('\n\n🎉 Job completed successfully!')
            if (status.result) {
                console.log('📊 Results:')
                console.log(`   - Products: ${status.result.product?.length || 0}`)
                console.log(`   - Colors: ${status.result.colors?.length || 0}`)
                console.log(`   - Sizes: ${status.result.sizes?.length || 0}`)
                console.log(`   - Variants: ${status.result.variant?.length || 0}`)
            }
            clearInterval(monitor)
            process.exit(0)
        } else if (status.state === 'failed') {
            console.log('\n\n💥 Job failed!')
            if (status.error) {
                console.log(`❌ Error: ${status.error}`)
            }
            clearInterval(monitor)
            process.exit(1)
        }
    }, 2000) // Check every 2 seconds
    
    // Handle Ctrl+C
    process.on('SIGINT', () => {
        console.log('\n\n👋 Monitoring stopped by user')
        clearInterval(monitor)
        process.exit(0)
    })
}

async function fetchQueueHealth() {
    try {
        const response = await fetch(`${BASE_URL}/api/enyes/system/products-queue-health`)
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }
        return await response.json()
    } catch (error) {
        console.error(`❌ Error fetching queue health: ${error.message}`)
        return null
    }
}

async function cleanupQueue(action = 'status') {
    try {
        const response = await fetch(`${BASE_URL}/api/enyes/system/products-queue-cleanup`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ action })
        })
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }
        return await response.json()
    } catch (error) {
        console.error(`❌ Error during cleanup: ${error.message}`)
        return null
    }
}

async function showQueueStatus() {
    const status = await fetchQueueStatus()
    const health = await fetchQueueHealth()

    if (!status) return

    console.log('\n📊 #### QUEUE STATUS ####')
    console.log(`Queue: ${status.queue}`)
    console.log(`Waiting: ${status.counts.waiting}`)
    console.log(`Active: ${status.counts.active}`)
    console.log(`Completed: ${status.counts.completed}`)
    console.log(`Failed: ${status.counts.failed}`)

    if (health) {
        const statusIcon = health.status === 'healthy' ? '💚' :
                          health.status === 'warning' ? '⚠️' : '🔴'
        console.log(`\n${statusIcon} Health: ${health.status} (${health.score}/100)`)

        if (health.issues.length > 0) {
            console.log('⚠️  Issues:')
            health.issues.forEach(issue => console.log(`   - ${issue}`))
        }

        if (health.potentially_stalled.length > 0) {
            console.log('🚨 Potentially Stalled Jobs:')
            health.potentially_stalled.forEach(job => {
                console.log(`   - ${job.id}: ${job.elapsed_minutes}min elapsed`)
            })
        }
    }

    if (status.jobs.active.length > 0) {
        console.log('\n🔄 Active Jobs:')
        status.jobs.active.forEach(job => {
            console.log(`   - ${job.id}: ${formatProgress(job.progress)}`)
        })
    }

    if (status.jobs.completed.length > 0) {
        console.log('\n✅ Recent Completed Jobs:')
        status.jobs.completed.slice(-3).forEach(job => {
            const duration = job.finishedOn && job.processedOn
                ? formatDuration(new Date(job.finishedOn) - new Date(job.processedOn))
                : 'unknown'
            console.log(`   - ${job.id}: completed in ${duration}`)
        })
    }

    console.log('========================\n')
}

async function main() {
    const args = process.argv.slice(2)
    const command = args[0]
    const subcommand = args[1]

    console.log('🚀 Products Queue Monitor')
    console.log(`📡 Connecting to: ${BASE_URL}`)

    if (command === 'status') {
        await showQueueStatus()
    } else if (command === 'test') {
        const jobId = await startTestJob()
        if (jobId) {
            await monitorJob(jobId)
        }
    } else if (command === 'health') {
        const health = await fetchQueueHealth()
        if (health) {
            const statusIcon = health.status === 'healthy' ? '💚' :
                              health.status === 'warning' ? '⚠️' : '🔴'
            console.log(`\n${statusIcon} Queue Health: ${health.status} (${health.score}/100)`)
            console.log(`📊 Performance: ${health.performance.avg_processing_time_formatted} avg processing time`)

            if (health.issues.length > 0) {
                console.log('\n⚠️  Issues:')
                health.issues.forEach(issue => console.log(`   - ${issue}`))
            }

            if (health.recommendations.length > 0) {
                console.log('\n💡 Recommendations:')
                health.recommendations.forEach(rec => console.log(`   - ${rec}`))
            }
        }
    } else if (command === 'cleanup') {
        const action = subcommand || 'status'
        console.log(`🧹 Running cleanup action: ${action}`)

        const result = await cleanupQueue(action)
        if (result) {
            console.log('✅ Cleanup completed:')
            console.log(JSON.stringify(result, null, 2))
        }
    } else if (command && command.length > 0 && !command.startsWith('-')) {
        // Assume it's a job ID
        await monitorJob(command)
    } else {
        console.log('\nUsage:')
        console.log('  node scripts/monitor-products-queue.js status              # Show queue status')
        console.log('  node scripts/monitor-products-queue.js health              # Show queue health')
        console.log('  node scripts/monitor-products-queue.js test                # Start test job and monitor')
        console.log('  node scripts/monitor-products-queue.js cleanup [action]    # Cleanup queue')
        console.log('    Actions: status, clean, drain, obliterate, retry-failed')
        console.log('  node scripts/monitor-products-queue.js <jobId>             # Monitor specific job')
        console.log('')

        // Show current status by default
        await showQueueStatus()
    }
}

// Check if fetch is available (Node.js 18+)
if (typeof fetch === 'undefined') {
    console.error('❌ This script requires Node.js 18+ with built-in fetch support')
    process.exit(1)
}

main().catch(error => {
    console.error(`💥 Unexpected error: ${error.message}`)
    process.exit(1)
})
