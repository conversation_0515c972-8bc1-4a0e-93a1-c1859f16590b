#!/usr/bin/env node

/**
 * Script para verificar que BullMQ esté configurado correctamente
 */

const BASE_URL = process.env.NUXT_SITE_URL || 'http://localhost:3000'

async function testRedisConnection() {
    console.log('🔍 Testing Redis connection...')
    try {
        const response = await fetch(`${BASE_URL}/api/enyes/system/redis-test`)
        const result = await response.json()
        
        if (result.success) {
            console.log('✅ Redis connection: OK')
            console.log(`   Host: ${result.config.host}:${result.config.port}`)
            console.log(`   Password: ${result.config.password_set ? 'SET' : 'NOT SET'}`)
        } else {
            console.log('❌ Redis connection: FAILED')
            console.log(`   Error: ${result.error.message}`)
            return false
        }
    } catch (error) {
        console.log('❌ Redis connection test failed:', error.message)
        return false
    }
    return true
}

async function testSimpleQueue() {
    console.log('\n🧪 Testing simple queue job...')
    try {
        const response = await fetch(`${BASE_URL}/api/enyes/system/test-simple-queue`, {
            method: 'POST'
        })
        const result = await response.json()
        
        if (result.success) {
            console.log('✅ Simple queue job: ADDED')
            console.log(`   Job ID: ${result.jobId}`)
            
            // Wait a moment and check status
            console.log('⏳ Waiting 3 seconds for job to process...')
            await new Promise(resolve => setTimeout(resolve, 3000))
            
            const statusResponse = await fetch(`${BASE_URL}/api/enyes/system/products-queue-status?jobId=${result.jobId}`)
            const status = await statusResponse.json()
            
            console.log(`📊 Job Status: ${status.state}`)
            if (status.state === 'completed') {
                console.log('✅ Simple queue job: COMPLETED')
                return true
            } else if (status.state === 'failed') {
                console.log('❌ Simple queue job: FAILED')
                console.log(`   Error: ${status.error}`)
                return false
            } else {
                console.log(`⏳ Simple queue job: ${status.state.toUpperCase()}`)
                return true
            }
        } else {
            console.log('❌ Simple queue job: FAILED TO ADD')
            return false
        }
    } catch (error) {
        console.log('❌ Simple queue test failed:', error.message)
        return false
    }
}

async function testQueueHealth() {
    console.log('\n💚 Testing queue health...')
    try {
        const response = await fetch(`${BASE_URL}/api/enyes/system/products-queue-health`)
        const result = await response.json()
        
        const statusIcon = result.status === 'healthy' ? '💚' : 
                          result.status === 'warning' ? '⚠️' : '🔴'
        
        console.log(`${statusIcon} Queue Health: ${result.status} (${result.score}/100)`)
        console.log(`📊 Queue Counts:`)
        console.log(`   Waiting: ${result.queue.counts.waiting}`)
        console.log(`   Active: ${result.queue.counts.active}`)
        console.log(`   Completed: ${result.queue.counts.completed}`)
        console.log(`   Failed: ${result.queue.counts.failed}`)
        
        if (result.issues.length > 0) {
            console.log('⚠️  Issues:')
            result.issues.forEach(issue => console.log(`   - ${issue}`))
        }
        
        return result.status !== 'error'
    } catch (error) {
        console.log('❌ Queue health test failed:', error.message)
        return false
    }
}

async function main() {
    console.log('🚀 BullMQ Setup Test')
    console.log(`📡 Testing server: ${BASE_URL}`)
    console.log('==========================================\n')
    
    let allTestsPassed = true
    
    // Test 1: Redis Connection
    const redisOk = await testRedisConnection()
    if (!redisOk) allTestsPassed = false
    
    // Test 2: Simple Queue Job
    const queueOk = await testSimpleQueue()
    if (!queueOk) allTestsPassed = false
    
    // Test 3: Queue Health
    const healthOk = await testQueueHealth()
    if (!healthOk) allTestsPassed = false
    
    console.log('\n==========================================')
    if (allTestsPassed) {
        console.log('🎉 All tests passed! BullMQ is working correctly.')
        console.log('\nYou can now run:')
        console.log('  curl -X POST http://localhost:3000/api/enyes/system/test-products-queue')
        console.log('  node scripts/monitor-products-queue.js test')
    } else {
        console.log('❌ Some tests failed. Check the errors above.')
        console.log('\nTroubleshooting:')
        console.log('  1. Make sure Redis is running: redis-cli ping')
        console.log('  2. Check environment variables: REDIS_HOST, REDIS_PORT')
        console.log('  3. Restart the Nuxt server')
        console.log('  4. Check server logs for worker initialization messages')
    }
    console.log('==========================================')
}

// Check if fetch is available (Node.js 18+)
if (typeof fetch === 'undefined') {
    console.error('❌ This script requires Node.js 18+ with built-in fetch support')
    process.exit(1)
}

main().catch(error => {
    console.error(`💥 Unexpected error: ${error.message}`)
    process.exit(1)
})
