export default defineNuxtRouteMiddleware(async (to:any, from:any) => {
    const { loggedIn }: any = useUserSession();
    const isAuthPage = to.name?.includes('auth') || null;
    const isProfilePage = to.name?.includes('profile') || null;

    const previousUrl = useCookie('previous_url', {
        sameSite: 'none',
        secure: true
    });

    if (to.name !== 'auth') previousUrl.value = to.path;
    
    if (loggedIn.value && isAuthPage) {
        return navigateTo('/');
    }

    if (!loggedIn.value && isProfilePage) {
        return navigateTo('/auth');
    }

    return;
});
