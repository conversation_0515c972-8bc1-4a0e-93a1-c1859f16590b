import { defineStore } from 'pinia';
import { type LineCart } from "../utils/interfaces";


export const useCartStore = defineStore('CartStore', {
    state: () => ({
        cart: {
            cartId: '',
            lines: [] as LineCart[],
            quantityTotal: 0,
            total: 0 as number
        }
    }),

    persist: true,

    // Getters
    getters: {
        getCart(state) {
            return {
                ...state.cart,
                total: state.cart.total / 100
            };
        },
        getLineById(state) {
            return (id: string): LineCart | null => {
                return state.cart.lines.find((item: LineCart) => item.id === id) || null;
            };
        }
    },

    // Actions
    actions: {
        updateCart(newCart: any) {
            this.cart = {
                ...newCart,
                total: Math.round((newCart.total || 0) * 100)
            };
        },
        addLine(line: LineCart) {
            if (!this.cart.cartId) {
                this.cart = {
                    cartId: useTools().getRandomString(15),
                    lines: [],
                    quantityTotal: 0,
                    total: 0
                };
            }

            const subtotalInCents = Math.round(line.subtotal * 100);
            const existingLineIndex = this.cart.lines.findIndex(item => item.id === line.id);
            let updatedLines = [...this.cart.lines];

            if (existingLineIndex !== -1) {
                const cartLine = updatedLines[existingLineIndex];
                const oldSubtotalInCents = Math.round((cartLine?.subtotal || 0) * 100);
                
                this.cart.quantityTotal -= cartLine?.quantity || 0;
                this.cart.total -= oldSubtotalInCents;

                updatedLines[existingLineIndex] = line;
            } else {
                updatedLines.push(line);
            }

            this.cart = {
                ...this.cart,
                lines: updatedLines,
                quantityTotal: this.cart.quantityTotal + line.quantity,
                total: this.cart.total + subtotalInCents
            };
        },

        deleteLine(ref: string) {
            const existingLineIndex = this.cart.lines.findIndex((item) => item.id === ref);
            if (existingLineIndex !== -1) {
                const cartLine = this.cart.lines[existingLineIndex];
                const updatedLines = [...this.cart.lines];
                updatedLines.splice(existingLineIndex, 1);
                const subtotalInCents = Math.round((cartLine?.subtotal || 0) * 100);
                
                this.cart = {
                    ...this.cart,
                    lines: updatedLines,
                    quantityTotal: this.cart.quantityTotal - (cartLine?.quantity || 0),
                    total: this.cart.total - subtotalInCents,
                };
            }
        },
        emptyCart() {
            this.cart = {
                cartId: '',
                lines: [],
                quantityTotal: 0,
                total: 0,
            };
        }
    }
})
