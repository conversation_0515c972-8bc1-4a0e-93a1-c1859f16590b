import { defineStore } from 'pinia';
import type { Discount } from '~/utils/interfaces';

export interface SocialNet {
    key: string;
    icon: string;
    link: string;
}

export const useInitialData = defineStore('InitialData', {
    state: () => ({
        config: {
            topBarMessage: '',
            socialNet: [] as SocialNet[],
            phoneContact: '',
            emailContact: '',
            carouselCategory: '',
            tax: 0,
            notify: [] as string[],
            shipping: 0,
            shippingFree: 0,
            discount: null as Discount | null,
            img1: '',
            img2: '',
            img3: '',
            img4: '',
            link1: '',
            link2: '',
            link3: '',
            link4: '',
            text1: '',
            text2: '',
            text3: '',
            text4: '',
            increase: 100
        }
    }),

    // Getters
    getters: {
        getConfig (state) {
            return state.config;
        }
    },

    // Actions
    actions: {
        addTopBarMessage(topBarMessage: any) {
            this.config.topBarMessage = topBarMessage;
        },
        pushSocialNet(socialNet: SocialNet) {
            this.config.socialNet.push(socialNet);
        },
        removeSocialNet(key: string) {
            this.config.socialNet = this.config.socialNet.filter(net => net.key !== key);
        },
        addPhoneContact(phoneContact: string) {
            this.config.phoneContact = phoneContact;
        },
        addEmailContact(emailContact: string) {
            this.config.emailContact = emailContact;
        },
        addCarouselCategory(carouselCategory: string) {
            this.config.carouselCategory = carouselCategory;
        },
        addTax(tax: number) {
            this.config.tax = tax / 100;
        },
        addNotify(notify: string[]) {
            this.config.notify = notify;
        },
        addShipping(shipping: number) {
            this.config.shipping = shipping;
        },
        addShippingFree(shippingFree: number) {
            this.config.shippingFree = shippingFree;
        },
        addDiscount(discount: Discount) {
            this.config.discount = discount;
        },
        addAll(config: any) {
            this.config.topBarMessage = config.top_bar_message;
            if (config.social_net && config.social_net?.length > 0) {
                config.social_net.forEach((sn: SocialNet) => {
                    this.config.socialNet.push(sn);
                });
            } else {
                this.config.socialNet = [];
            }
            this.config.phoneContact = config?.phone_contact;
            this.config.emailContact = config?.email_contact;
            this.config.carouselCategory = config?.carousel_category;
            this.config.tax = (config?.tax ?? 0) / 100;
            this.config.notify = config?.notify;
            this.config.shipping = (config?.shipping ?? 0) / 100;
            this.config.shippingFree = (config?.shipping_free ?? 0) / 100;
            this.config.discount = config?.discount || null;
            this.config.img1 = config?.img_1;
            this.config.img2 = config?.img_2;
            this.config.img3 = config?.img_3;
            this.config.img4 = config?.img_4;
            this.config.link1 = config?.link_1;
            this.config.link2 = config?.link_2;
            this.config.link3 = config?.link_3;
            this.config.link4 = config?.link_4;
            this.config.text1 = config?.text_1;
            this.config.text2 = config?.text_2;
            this.config.text3 = config?.text_3;
            this.config.text4 = config?.text_4;
            this.config.increase = config?.increase || 100;
        }
    }
});
