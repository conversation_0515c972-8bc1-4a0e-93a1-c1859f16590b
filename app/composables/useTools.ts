export const useTools = () => {
    const formatPhoneNumber = (phone: string): string | null => {
        let cleanedPhone = phone.replace(/\s+/g, '');
      
        if (cleanedPhone.startsWith('+34')) {
            cleanedPhone = cleanedPhone.slice(1);
        } else if (!cleanedPhone.startsWith('34')) {
            cleanedPhone = '34' + cleanedPhone;
        }

        return cleanedPhone.length === 11 ? cleanedPhone : null;
    }

    const truncateText = (text: string, maxChars: number) => {
        if (text.length <= maxChars) return text;
  
        const truncatedText = text.slice(0, maxChars + 1);
        const lastSpaceIndex = truncatedText.lastIndexOf(" ");
        
        return lastSpaceIndex > 0 
            ? truncatedText.slice(0, lastSpaceIndex) + "..." 
            : truncatedText.trim() + "...";
    }

    const getRandomString = (length:number) => {
        const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let randomString = '';
        for (var i = 0; i < length; i++) {
            var randomIndex = Math.floor(Math.random() * characters.length);
            randomString += characters[randomIndex];
        }
        return randomString;
    }

    const startTimer = (durationInMinutes: number, onTick: Function | null, onComplete: Function) => {
        const durationInMilliseconds = durationInMinutes * 60 * 1000;
        let remainingTime = durationInMilliseconds;
        let interval: ReturnType<typeof setInterval> | undefined;

        if (onTick) {
            interval = setInterval(() => {
                onTick(remainingTime);
                remainingTime -= 1000;

                if (remainingTime <= 0) {
                    clearInterval(interval);
                    if (onComplete) onComplete();
                }
            }, 1000);
        }

        setTimeout(() => {
            if (interval) clearInterval(interval);
            if (onComplete) onComplete();
        }, durationInMilliseconds);
    };

    const formatDate = (date: string) => {
        const newDate = new Date(date);

        const dia = String(newDate.getDate()).padStart(2, '0');
        const mes = String(newDate.getMonth() + 1).padStart(2, '0');
        const año = newDate.getFullYear();
        const horas = String(newDate.getHours()).padStart(2, '0');
        const minutos = String(newDate.getMinutes()).padStart(2, '0');
        const segundos = String(newDate.getSeconds()).padStart(2, '0');

        return `${dia}-${mes}-${año} ${horas}:${minutos}:${segundos}`;
    }

    const removeEmails = (text: string) => {
        const emailPattern = /(?:[a-zA-Z0-9_.+-]+(?: ?[aA@][tT] ?| ?@ ?)|(?:[a-zA-Z0-9]+ ?)){1,3}(?:gmail|yahoo|hotmail|outlook|mail|g ?m ?a ?i ?l|y ?a ?h ?o ?o|h ?o ?t ?m ?a ?i ?l|o ?u ?t ?l ?o ?o ?k) ?(?:\. ?[a-zA-Z]{2,4})?/gi;
        return text.replace(emailPattern, '[ESTE CONTENIDO HA SIDO MODIFICADO POR EL SISTEMA DE MODERACIÓN PARA CUMPLIR CON LAS NORMAS]');
    }

    const removePhoneNumbers = (text: string) => {
        const phonePattern = /(?:\d[^\da-zA-Z]*?){7,15}/g;
        return text.replace(phonePattern, '[ESTE CONTENIDO HA SIDO MODIFICADO POR EL SISTEMA DE MODERACIÓN PARA CUMPLIR CON LAS NORMAS]');
    }

    const moderateContent = (text: string) => {
        let sanitizedText = removeEmails(text);
        sanitizedText = removePhoneNumbers(sanitizedText);
        return sanitizedText;
    }

    const addLineBreaks = (text: string) => {
        return text.replace(/(\.)\s*/g, "$1\n").replace(/·/g, "✓ ");
    }

    return {
        formatPhoneNumber,
        truncateText,
        getRandomString,
        startTimer,
        formatDate,
        moderateContent,
        addLineBreaks
    }
};
