export const useEnyesShop = () => {
    const systemConfig = useRuntimeConfig();

    const getColors = () => {
        const { data, error, status, refresh } = useFetch<{ data: { colors: string[] } }>(
            `/api/enyes/store/color`, {
                method: 'GET',
            }
        );
    
        const colors = computed(() => data.value?.data?.colors ?? []);
        const loading = computed(() => status.value === 'pending' || status.value === 'idle');
    
        return { 
            colors,
            loadingColors: loading,
            error,
            refresh 
        };
    }

    const getCategories = (highlight: boolean = false) => {
        const { data, error, status, refresh } = useFetch<{ data: { categories: any[] } }>(
            '/api/enyes/store/categories', {
                method: 'GET',
                query: { highlight },
            }
        );
        const categories = computed(() => data.value?.data?.categories ?? []);
        const loading = computed(() => status.value === 'pending' || status.value === 'idle');
        return {
            categories,
            loading,
            error,
            refresh
        };
    }

    const getSizes = () => {
        const { data, error, status, refresh } = useFetch<{ data: { sizes: string[] } }>(
            `/api/enyes/store/size`, {
                method: 'GET',
                lazy: true,
                server: false
            }
        );
    
        const sizes = computed(() => data.value?.data.sizes || []);
        const loading = computed(() => status.value === 'pending' || status.value === 'idle');
    
        return { 
            sizes,
            loadingSizes: loading,
            error,
            refresh 
        };
    };

    const getProducts = async (query: any) => {
        const products = ref([])
        const total = ref(0)

        try {
            const data: any = await $fetch(`/api/enyes/store/product`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ data: query }),
            })
    
            products.value = data.data?.products
            total.value = data.data?.total?.aggregate?.count
        } catch (error) {
            console.error('Error fetching products:', error)
        }

        return { products, total }
    }

    const getMaterials = async (query: any) => {
        const materials = ref<string[]>([])

        try {
            const data: any = await $fetch(`/api/enyes/store/materials`, {
                method: 'GET',
                query: { ...query }
            })

            materials.value = data.data.materials || []
        } catch (error) {
            console.error('Error fetching materials:', error)
        }

        return materials
    }

    const getProduct = (slug: string) => {
        const data = ref(null)
        const error = ref(null)
        const loading = ref(true)

        useFetch(`/api/enyes/store/product/${slug}`, { method: 'GET' }).then((res: any) => {
            data.value = res.data.value?.data?.product
        }).catch((err) => {
            error.value = err
        }).finally(() => {
            loading.value = false
        })

        return { 
            product: computed(() => data.value),
            loading, 
            error 
        }
    }

    const getAddresses = async () => {
        const { data, error, status, refresh } = useFetch<any>(
            `/api/enyes/auth/profile/address`, {
                method: 'GET',
            }
        )

        const addresses = computed(() => data.value?.data?.user?.addresses_aggregate ?? []);
        const loading = computed(() => status.value === 'pending' || status.value === 'idle');

        return { 
            addresses,
            loading,
            error,
            refresh 
        };
    }

    const insertAddress = async (body: any) => {
        const { data }: any = await $fetch('/api/enyes/auth/profile/address', {
            method: 'POST',
            body: JSON.stringify(body)
        });
        return data;
    }

    const getSales = async () => {
        const { data, error, status, refresh } = useFetch<any>(
            `/api/enyes/auth/profile/sales`, {
                method: 'GET',
            }
        );

        const sales = computed(() => data.value?.data?.user?.sales_aggregate ?? []);
        const loading = computed(() => status.value === 'pending' || status.value === 'idle');

        return { 
            sales,
            loading,
            error,
            refresh 
        };
    }

    const calculateMarkingPrices = (items: any[], quantity: number): any => {
        const result: Record<string, { price: number }> = {}
        items.forEach((item: any) => {
            const p = item.print.print
            let price = p.price5
            if (quantity < p.amountunder1) price = p.price1
            else if (quantity < p.amountunder2) price = p.price2
            else if (quantity < p.amountunder3) price = p.price3
            else if (quantity < p.amountunder4) price = p.price4
            const key = `${p.id}-${item.area.id}`
            const cliche: number = item.clicheRep ? (item.print.print.clicherep / 1000) : (item.print.print.cliche / 1000) || 0

            if (item.print.print.minjob > 0 && (quantity * price / 1000) <= (item.print.print.minjob / 1000)) {
                result[key] = { price: (item.print.print.minjob / 1000), cliche, minJob: true, repetitions: item.clicheRep } as { price: number, cliche: number, minJob: boolean, repetitions: boolean }
            } else {
                result[key] = { price:( price / 1000), cliche, minJob: false, repetitions: item.clicheRep } as { price: number, cliche: number, minJob: boolean, repetitions: boolean }
            }
        })
        return result
    }

    const markingTotal = (items: any[], quantity: number): number => {
        const allMarking = calculateMarkingPrices(items, quantity)
        let markTotal = 0 
        Object.values(allMarking).forEach((item: any) => {
            if (item.minJob) markTotal += (item.price + item.cliche)
            else markTotal += ((item.price * quantity) + item.cliche)
            
        })

        const total = markTotal
        return total
    }

    const roundTwo = (num: number) => Math.round((num + Number.EPSILON) * 100) / 100

    return {
        getColors,
        getCategories,
        getSizes,
        getProducts,
        getProduct,
        getAddresses,
        insertAddress,
        getSales,
        calculateMarkingPrices,
        markingTotal,
        getMaterials,
        roundTwo
    }
}
