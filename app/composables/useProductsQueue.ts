export interface QueueJob {
  jobId: string
  state: 'waiting' | 'active' | 'completed' | 'failed' | 'delayed'
  progress: number | object
  data: any
  result?: any
  error?: string
  createdAt: Date
  processedOn?: Date
  finishedOn?: Date
}

export interface QueueStatus {
  queue: string
  counts: {
    waiting: number
    active: number
    completed: number
    failed: number
  }
  jobs: {
    waiting: Queue<PERSON>ob[]
    active: Queue<PERSON>ob[]
    completed: QueueJob[]
    failed: QueueJob[]
  }
}

export const useProductsQueue = () => {
  const isProcessing = ref(false)
  const currentJob = ref<QueueJob | null>(null)
  const queueStatus = ref<QueueStatus | null>(null)

  /**
   * Procesa productos usando la cola de BullMQ
   */
  const processProducts = async (useQueue: boolean = true) => {
    try {
      isProcessing.value = true
      
      const { data } = await $fetch<{
        success: boolean
        jobId?: string
        message: string
        useQueue: boolean
        [key: string]: any
      }>('/api/enyes/system/products', {
        method: 'POST',
        body: { useQueue }
      })

      if (useQueue && data.jobId) {
        // Si usa cola, comenzar a monitorear el trabajo
        await monitorJob(data.jobId)
      }

      return data
    } catch (error) {
      console.error('Error processing products:', error)
      throw error
    } finally {
      isProcessing.value = false
    }
  }

  /**
   * Agrega un trabajo a la cola de productos
   */
  const queueProducts = async () => {
    try {
      const { data } = await $fetch<{
        success: boolean
        jobId: string
        message: string
        status: string
      }>('/api/enyes/system/products-queue', {
        method: 'POST'
      })

      if (data.jobId) {
        await monitorJob(data.jobId)
      }

      return data
    } catch (error) {
      console.error('Error queuing products:', error)
      throw error
    }
  }

  /**
   * Obtiene el estado de un trabajo específico
   */
  const getJobStatus = async (jobId: string): Promise<QueueJob> => {
    try {
      const data = await $fetch<QueueJob>(`/api/enyes/system/products-queue-status?jobId=${jobId}`)
      return data
    } catch (error) {
      console.error('Error getting job status:', error)
      throw error
    }
  }

  /**
   * Obtiene el estado general de la cola
   */
  const getQueueStatus = async (): Promise<QueueStatus> => {
    try {
      const data = await $fetch<QueueStatus>('/api/enyes/system/products-queue-status')
      queueStatus.value = data
      return data
    } catch (error) {
      console.error('Error getting queue status:', error)
      throw error
    }
  }

  /**
   * Monitorea un trabajo hasta que se complete o falle
   */
  const monitorJob = async (jobId: string, intervalMs: number = 2000) => {
    return new Promise<QueueJob>((resolve, reject) => {
      const checkStatus = async () => {
        try {
          const job = await getJobStatus(jobId)
          currentJob.value = job

          if (job.state === 'completed') {
            resolve(job)
          } else if (job.state === 'failed') {
            reject(new Error(job.error || 'Job failed'))
          } else {
            // Continuar monitoreando
            setTimeout(checkStatus, intervalMs)
          }
        } catch (error) {
          reject(error)
        }
      }

      checkStatus()
    })
  }

  /**
   * Formatea el progreso para mostrar
   */
  const formatProgress = (progress: number | object): string => {
    if (typeof progress === 'number') {
      return `${progress}%`
    }
    if (typeof progress === 'object' && progress !== null) {
      return JSON.stringify(progress)
    }
    return '0%'
  }

  /**
   * Formatea el estado para mostrar
   */
  const formatState = (state: string): string => {
    const stateMap: Record<string, string> = {
      waiting: 'En espera',
      active: 'Procesando',
      completed: 'Completado',
      failed: 'Falló',
      delayed: 'Retrasado'
    }
    return stateMap[state] || state
  }

  /**
   * Obtiene el color del estado para la UI
   */
  const getStateColor = (state: string): string => {
    const colorMap: Record<string, string> = {
      waiting: 'text-yellow-600',
      active: 'text-blue-600',
      completed: 'text-green-600',
      failed: 'text-red-600',
      delayed: 'text-orange-600'
    }
    return colorMap[state] || 'text-gray-600'
  }

  return {
    // Estado reactivo
    isProcessing: readonly(isProcessing),
    currentJob: readonly(currentJob),
    queueStatus: readonly(queueStatus),

    // Métodos
    processProducts,
    queueProducts,
    getJobStatus,
    getQueueStatus,
    monitorJob,

    // Utilidades
    formatProgress,
    formatState,
    getStateColor
  }
}
