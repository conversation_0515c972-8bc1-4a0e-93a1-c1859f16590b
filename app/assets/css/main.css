@import url('https://fonts.googleapis.com/css2?family=Share+Tech+Mono&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Russo+One&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Josefin+Sans:wght@700&display=swap');

body, button, input, select, textarea {
    font-family: 'Poppins', sans-serif, 'Outfit';
    @apply bg-white text-slate-900;
    font-size: 14px !important;
}

.cookieControl__BarButtons button:last-child {
    display: none !important;
}

.container-info {
    width: 100% !important;
}
@media (min-width: 640px) {
    .container-info {
        max-width: 640px !important;
    }
}
@media (min-width: 768px) {
    .container-info {
        max-width: 768px !important;
    }
}
@media (min-width: 1024px) {
    .container-info {
        max-width: 924px !important;
    }
}
@media (min-width: 1280px) {
    .container-info {
        max-width: 1140px !important;
    }
}
@media (min-width: 1536px) {
    .container-info {
        max-width: 1140px !important;
    }
}

.russo-one {
  font-family: 'Russo One' !important;
}

.josefin-title {
  font-family: 'Josefin Sans' !important;
  font-weight: 700 !important;
}
