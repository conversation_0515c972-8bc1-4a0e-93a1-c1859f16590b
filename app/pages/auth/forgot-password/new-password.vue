<script setup lang="ts">
    import { z } from 'zod'
    import { useToast } from 'primevue/usetoast';
    import type { FormData, FormConfig } from '~/components/form/FormTypes';
    import DynamicForm from "../../../components/form/DynamicForm.vue";
    import { useTools } from '~/composables/useTools';

    const route = useRoute();
    const loading = ref(false);
    const { t } = useI18n();
    const toast = useToast();
    const queryEmail = route.query?.email?.toString() || '';
    const email = ref(queryEmail.toLowerCase());
    const disabled = ref(false);
    const durationInMinutes = 2;
    const remainingTime = ref(durationInMinutes * 60);

    const formConfig: FormConfig = {
        stepByStep: false,
        submitLabel: 'ENVIAR',
        loadingLabel: 'Enviando...',
        labelType: 'Standard',
        fields: [
            {
                name: 'email',
                label: '<PERSON><PERSON><PERSON>',
                component: 'InputText',
                props: {
                    type: 'email',
                    autofocus: true
                },
                validation: z.string().email({ message: "Ingresa un email válido" }),
                defaultValue: email.value,
                required: true
            }, {
                name: 'password',
                label: 'Su Nueva Contraseña',
                component: 'Password',
                props: {
                    promptLabel: "Elije una contraseña",
                    weakLabel: "Insegura",
                    mediumLabel: "Promedio",
                    strongLabel: "Segura",
                    toggleMask: true
                },
                validation: z.string().min(8, { message: "La contraseña debe tener al menos 8 caracteres" }),
                defaultValue: '',
                required: true
            }, {
                name: 'otp',
                component: 'InputOtp',
                props: {
                    autofocus: true,
                    integerOnly: true,
                    length: 6
                },
                validation: z.string({ message: "Ingresa el código recibido" }),
                defaultValue: '',
                required: true
            }
        ]
    };

    const formattedTime = computed(() => {
        const minutes = Math.floor(remainingTime.value / 60);
        const seconds = remainingTime.value % 60;
        return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
    });

    const handleSubmit = async (form: FormData) => {
        if (form.otp && form.otp.length === 6) {
            loading.value = true;

            try {
                const { data } = await $fetch('/api/enyes/auth/password/update', {
                    method: 'POST',
                    body: { email: form.email.toLowerCase(), code: form.otp, password: form.password },
                });

                if (data.status === 'success') {
                    navigateTo(`/auth`);
                    return;
                }

                toast.add({ 
                    severity: 'error', 
                    summary: 'Ocurrió un error', 
                    detail: t('error.unknownError') || 'error.unknownError', 
                life: 5000 });
            } catch (err: any) {
                toast.add({ 
                    severity: 'error', 
                    summary: 'Ocurrió un error', 
                    detail: t(err.data?.message) || 
                        t(err.message) || 
                        t(err.response.errors[0].message) ||
                        t('error.unknownError') || 'error.unknownError', 
                    life: 3000 });
            } finally {
                loading.value = false;
            }
        } else {
            toast.add({ 
                severity: 'warn', 
                summary: 'Código incorrecto', 
                detail: 'El código no corersponde.', 
                life: 3000 
            });
        }
    }

    const handleUpdate = (event: any) => {
        if(event.code === 'email') email.value = event.data.toLowerCase();
    }

    const resend = async () => {
        if (email.value) {
            loading.value = true;

            try {
                const data = await $fetch('/api/auth/password/forgot', {
                    method: 'POST',
                    body: { email: email.value },
                });
                disabled.value = true;
                useTools().startTimer(durationInMinutes, () => {
                    remainingTime.value--;
                }, () => {
                    disabled.value = false;
                    remainingTime.value = durationInMinutes * 60;
                });
            } catch (err: any) {
                toast.add({ 
                    severity: 'error', 
                    summary: 'Ocurrió un error', 
                    detail: t(err.data?.message) || 
                        t(err.message) || 
                        t(err.response.errors[0].message) ||
                        t('error.unknownError') || 'error.unknownError', 
                    life: 3000 });
            } finally {
                loading.value = false;
            }
        } else {
            toast.add({ 
                severity: 'info', 
                summary: 'Correo Electrónico Requerido', 
                detail: 'Debe ingresar su correo electrónico.', 
                life: 5000 
            });
        }
    }
</script>

<template>
    <div class="w-full min-full flex justify-center items-center py-8 md:py-28">
        <Toast />
        <div class="w-[460px]">
            <DynamicForm
                :formConfig="formConfig"
                v-model:loading="loading"
                @submit="handleSubmit"
                @update:modelValue="handleUpdate"
            >
                <template #alternativeTitle>
                    <div class="w-full h-full flex flex-col justify-center items-center pb-10 gap-4">
                        <div class="flex flex-col items-center gap-2">
                            <h5 class="text-2xl">Actualizar Contraseña</h5>
                            <p class="text-sm font-light flex gap-2 text-center">
                                Ingresa tu nueva contraseña y el código recibido en tu correo electrónico
                            </p>
                            <div v-if="!disabled" @click="resend" class="cursor-pointer">
                                <div class="!font-semibold p-0 text-sky-600 hover:text-sky-900 underline underline-offset-2 decoration-2 decoration-sky-600">
                                    ¿No recibió el código?
                                </div>
                            </div>

                            <div v-else>
                                {{ formattedTime }}
                            </div>
                        </div>
                    </div>
                </template>
            </DynamicForm>
        </div>
    </div>
</template>
