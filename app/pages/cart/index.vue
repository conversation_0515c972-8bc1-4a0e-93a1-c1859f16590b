<script setup lang="ts">
    import { useToast } from "primevue/usetoast";
    import type { Discount } from "~/utils/interfaces";

    const { user, loggedIn }: any = useUserSession();
    const cartStore = useCartStore();
    const cart = toRef(cartStore.getCart);
    const config: any = useInitialData().getConfig;
    const configSystem = useRuntimeConfig();
    const address= ref<any>(null);
    const toast = useToast();
    const { t } = useI18n();
    const shipmentSelected = ref<string>('sending'); // collection or sending
    const shipping = ref<number>(0);
    const route = useRoute();
    const step = ref<string>('1');
    const loading = ref<boolean>(false);

    const removeLine = (id: string) => {
        cartStore.deleteLine(id);
        cart.value = cartStore.getCart;
    }

    // #### Calculate Marking Prices ####
    // -------------------------------------------------------
    const prices = ref({}) as any
    // -------------------------------------------------------
    // #### End Calculate Marking Prices ####

    // #### Paypal Success ####
    // -------------------------------------------------------
    const successPayment = async (response: any, status: string = 'Pago Recibido') => {
        const cartPaid = JSON.parse(JSON.stringify({ ...cart.value, summary: summary.value }));
        loading.value = true;

        try {
            // **** Queue payment ****
            await $fetch('/api/enyes/system/queue/send', {
                method: 'POST',
                body: { data: { paypal: response, cart: { ...cartPaid, address_id: address.value?.id || null }, status }, processor: 'payment-paypal' }
            });

            // **** Notify payment ****
            await $fetch('/api/enyes/system/event/send', {
                method: 'POST',
                body: { 
                    data: { 
                        customer: user.value?.first_name,
                        url: `${ configSystem.public.site.url }/profile/orders`,
                        support_email: config.emailContact,
                        cart_id: cartPaid.cartId,
                        email_customer: user.value?.email,
                        phone_customer: user.value?.phone,
                        total: `${ response.purchase_units[0].payments.captures[0]?.amount?.value } ${ response.purchase_units[0].payments.captures[0]?.amount?.currency_code }`,
                        notify_email: config.notify
                    }, event: 'new:sale'
                }
            });

            loading.value = false;
            cartStore.emptyCart();
            navigateTo(`/profile/orders`);
        } catch (err: any) {
            loading.value = false;
            toast.add({ 
                severity: 'error', 
                summary: 'Ocurrió un error', 
                detail: t(err?.response?._data?.message) || 
                    t(err?.message) ||
                    t(err?.data?.message) ||
                    t('error.unknownError') || 'error.unknownError', 
                life: 3000 });
        }
    }

    const alternativePayment = async (data: any) => {
        const response = {
            id: data.reference,
            type: (data.type === 'bizum') ? 'Bizum' : 'Transferencia',
            payer: {
                address: {
                    country_code: 'ES'
                },
                email_address: user.value?.email,
                name: {
                    given_name: user.value?.first_name,
                    surname: ''
                },
                payer_id: ''
            },
            purchase_units: [
                {
                    payments: {
                        captures: [
                            {
                                amount: {
                                    currency_code: 'EUR',
                                    value: summary.value.total.toFixed(2)
                                },
                                create_time: new Date().toISOString(),
                                seller_receivable_breakdown: {
                                    net_amount: {
                                        value: summary.value.total.toFixed(2)
                                    },
                                    paypal_fee: {
                                        value: '0'
                                    }
                                }
                            }
                        ]
                    }
                }
            ]
        }
        await successPayment(response, 'Pendiente Verificación');
    }
    // -------------------------------------------------------
    // #### End Paypal Success ####

    // #### Calculate Summary ####
    // -------------------------------------------------------
    const getDiscount = (amount: number): number => {
        const userDiscount: Discount | null = user.value?.discount?.[0] ?? null;

        if (userDiscount) {
            return userDiscount.Tipo === 'percent'
                ? amount * (userDiscount.Monto / 100)
                : userDiscount.Monto;
        }

        const configDiscount: Discount | null = config?.discount?.[0] ?? null;
        if (!configDiscount || configDiscount?.Monto == null) return 0;

        const from = configDiscount?.Desde ?? 0;
        if (amount < from) return 0;

        return configDiscount?.Tipo === 'percent'
            ? amount * (configDiscount?.Monto / 100)
            : configDiscount?.Monto;
    }

    const getMarkingTotal = (): number => {
        let total = 0

        if (prices.value && typeof prices.value === 'object' && !Array.isArray(prices.value) && Object.keys(prices.value).length > 0) {
            Object.entries(prices.value).forEach(([key, product]: any) => {
                Object.values(product).forEach((element: any) => {
                    const product = cart.value.lines?.find((obj: any) => obj.id === key) || null
                    const quantity = (product) ? product?.quantity : 0
                    const price = (element.minJob) ? element.price : element.price * quantity
                    total += price + element.cliche || 0
                })
            })
        }
        return total
    }

    const summary = computed(() => {
        const amount = ((useEnyesShop().roundTwo(cart?.value.total) + getMarkingTotal()) || 0)
        shipping.value = (amount > 0 && amount >= config.shippingFree) ? 0 : config.shipping
        const shipment = ((shipmentSelected.value === 'sending')) ? shipping.value : 0
        const discount = getDiscount(amount)
        const subtotal = amount + shipment - discount
        const tax = subtotal * (config?.tax / 100)
        if (shipmentSelected.value === 'collection') address.value = null
        return {
            amount: useEnyesShop().roundTwo(amount),
            shipment: useEnyesShop().roundTwo(shipment),
            shipmentType: shipmentSelected.value,
            discount: useEnyesShop().roundTwo(discount),
            subtotal: useEnyesShop().roundTwo(subtotal),
            tax: useEnyesShop().roundTwo(tax),
            total: useEnyesShop().roundTwo(subtotal) + useEnyesShop().roundTwo(tax),
        }
    })
    // -------------------------------------------------------
    // #### End Calculate Summary ####

    watch(() => cart.value.lines, (newLines: any) => {
        newLines.forEach((line: any) => {
            prices.value[line.id] = useEnyesShop().calculateMarkingPrices(line?.markings, line?.quantity)
        })
    }, { immediate: true });

    onMounted(() => {
        step.value = route.query.step?.toString() || '1';
    });
</script>

<template>
    <div>
        <Toast />
        <div class="container w-full h-full mx-auto px-2 md:px-4 my-8">
            <div class="w-full h-full border border-slate-200 rounded-lg p-4 sm:px-8 sm:pt-6 sm:pb-8 lg:px-16 lg:pt-6 lg:pb-16">
                <ClientOnly >
                    <Stepper :value="step" linear>
                        <StepList>
                            <Step value="1">Resumen</Step>
                            <Step value="2">Datos</Step>
                            <Step value="3">Pagar</Step>
                        </StepList>

                        <StepPanels>
                            <StepPanel v-slot="{ activateCallback }" value="1">
                                <div v-if="cart?.lines?.length > 0" class="flex pb-6 justify-end">
                                    <Button size="small" @click="activateCallback('2'); step = '2'">
                                        Siguiente <Icon name="akar-icons:arrow-right" />
                                    </Button>
                                </div>
                                <div class="w-full text-lg font-semibold mb-3 flex flex-col gap-2">
                                    <div>Carrito de compras (Resumen)</div>
                                </div>
                                <div class="flex justify-center">
                                    <PurchaseSummary :cart="cart" @removeLine="removeLine" :step="step" :total="summary?.amount" :prices="prices" />
                                </div>
                                <div v-if="cart?.lines?.length > 0" class="flex pt-6 justify-end">
                                    <Button size="small" @click="activateCallback('2'); step = '2'">
                                        Siguiente <Icon name="akar-icons:arrow-right" />
                                    </Button>
                                </div>
                            </StepPanel>

                            <StepPanel v-slot="{ activateCallback }" value="2">
                                <div v-if="cart?.lines?.length > 0" class="flex pb-6 justify-between">
                                    <Button size="small" @click="activateCallback('1'); step = '1'">
                                        <Icon name="akar-icons:arrow-left" /> Anterior
                                    </Button>
                                    <Button 
                                        :disabled="!loggedIn || !((address && summary?.shipmentType === 'sending') || summary?.shipmentType === 'collection')" 
                                        size="small" @click="activateCallback('3'); step = '3'"
                                        v-tooltip="!((address && summary?.shipmentType === 'sending') || summary?.shipmentType === 'collection') && 'Debe seleccionar una dirección o Recoger en tienda'"
                                    >
                                        Siguiente <Icon name="akar-icons:arrow-right" />
                                    </Button>
                                </div>
                                <div class="w-full flex flex-col gap-2">
                                    <div v-if="loggedIn" class="flex flex-col gap-6">
                                        <div class="flex flex-col gap-2">
                                            <div class="text-xl font-semibold">1. Opción de entrega</div>
                                            <div class="border border-slate-200 rounded-lg p-4 flex flex-col gap-2">
                                                <span class="font-semibold">Envío:</span>
                                                {{ shipmentSelected }}
                                                <div class="w-full">
                                                    <div class="flex items-center gap-2">
                                                        <RadioButton v-model="shipmentSelected" inputId="sending" value="sending" />
                                                        <label for="sending" class="!w-full">
                                                            <div class="!w-full flex justify-between">
                                                                <div class="font-light">Envío a domicilio:</div>
                                                                <div class="text-right font-semibold">€ {{ shipping.toFixed(2) }}</div>
                                                            </div>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="w-full">
                                                    <div class="flex items-center gap-2">
                                                        <RadioButton v-model="shipmentSelected" inputId="collection" value="collection" />
                                                        <label for="collection" class="!w-full">
                                                            <div class="!w-full flex justify-between">
                                                                <div class="font-light">Recoger en tienda:</div>
                                                                <div class="text-right font-semibold">gratis</div>
                                                            </div>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>

                                            <transition 
                                                enter-active-class="transition-all duration-300 ease-out"
                                                enter-from-class="max-h-0 opacity-0"
                                                enter-to-class="max-h-[500px] opacity-100"
                                                leave-active-class="transition-all duration-300 ease-in"
                                                leave-from-class="max-h-[500px] opacity-100"
                                                leave-to-class="max-h-0 opacity-0"
                                            >
                                                <div v-if="shipmentSelected === 'sending'">
                                                    
                                                    <div class="w-full p-4 border border-slate-200 rounded-lg">
                                                        <div class="w-full font-semibold mb-2 flex flex-col gap-2">
                                                            <div class="text-sm font-light">Seleccione o agregue una dirección</div>
                                                        </div>
                                                        <Addresses :selectable="true" @select="($event) => address = $event" />
                                                    </div>
                                                </div>
                                            </transition>
                                        </div>
                                        
                                        <div class="flex flex-col gap-2">
                                            <div class="text-xl">2. Datos del cliente</div>
                                            <div class="border border-slate-200 rounded-lg p-4 flex flex-col gap-2">
                                                <div class="w-full flex justify-end">
                                                    <span class="cursor-pointer text-sm hover:text-sky-600" @click="() => navigateTo('/profile')">Editar</span>
                                                </div>
                                                <div class="w-full md:w-1/2">
                                                    <div class="w-full flex justify-between">
                                                        <span>Nombre:</span>
                                                        <span>{{ user?.first_name }}</span>
                                                    </div>
                                                    <div class="w-full flex justify-between">
                                                        <span>Correo electrónico:</span>
                                                        <span>{{ user?.email }}</span>
                                                    </div>
                                                    <div class="w-full flex justify-between">
                                                        <span>Número de teléfono:</span>
                                                        <span>{{ user?.phone }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div v-else class="w-full flex flex-col gap-4">
                                        <div class="text-lg font-semibold">Debe iniciar sesión o registrarse para continuar.</div>
                                        <div class="w-full flex justify-center">
                                            <Tabs value="0">
                                                <TabList>
                                                    <Tab value="0">Inicia Sesión</Tab>
                                                    <Tab value="1">Regístrate</Tab>
                                                </TabList>
                                                <TabPanels>
                                                    <TabPanel value="0">
                                                        <Login :extra="false" />
                                                    </TabPanel>
                                                    <TabPanel value="1">
                                                        <Register :extra="false" />
                                                    </TabPanel>
                                                </TabPanels>
                                            </Tabs>
                                        </div>
                                    </div>
                                </div>
                                <div v-if="cart?.lines?.length > 0" class="flex pt-6 justify-between">
                                    <Button size="small" @click="activateCallback('1'); step = '1'">
                                        <Icon name="akar-icons:arrow-left" /> Anterior
                                    </Button>
                                    <Button 
                                        :disabled="!loggedIn || !((address && summary?.shipmentType === 'sending') || summary?.shipmentType === 'collection')" 
                                        size="small" @click="activateCallback('3'); step = '3'"
                                        v-tooltip="!((address && summary?.shipmentType === 'sending') || summary?.shipmentType === 'collection') && 'Debe seleccionar una dirección o Recoger en tienda'"
                                    >
                                        Siguiente <Icon name="akar-icons:arrow-right" />
                                    </Button>
                                </div>
                            </StepPanel>

                            <StepPanel v-slot="{ activateCallback }" value="3">
                                <BlockUI :blocked="loading">
                                    <div v-if="cart?.lines?.length > 0" class="flex pb-6">
                                        <Button size="small" @click="activateCallback('2'); step = '2'">
                                            <Icon name="akar-icons:arrow-left" /> Anterior
                                        </Button>
                                    </div>
                                    <div v-if="loggedIn && ((address && summary?.shipmentType === 'sending') || summary?.shipmentType === 'collection')" class="overflow-hidden ">
                                        <div class="w-full h-full flex flex-col md:flex-row gap-6">
                                            <PurchaseSummary :cart="cart" @removeLine="removeLine" :step="step" :prices="prices" />
                                            <div class="w-full h-fit md:w-4/12 flex flex-col gap-2">
                                                <div class="w-full p-4 border border-slate-200 rounded-lg flex flex-col gap-4">
                                                    <div class="w-full text-lg font-semibold mb-2">Resumen</div>

                                                    <div class="grid grid-cols-2">
                                                        <div class="font-light">
                                                            Importe:
                                                        </div>
                                                        <div class="text-right font-semibold">
                                                            € {{ summary?.amount?.toFixed(2) }}
                                                        </div>
                                                    </div>

                                                    <div>
                                                        <div v-if="summary?.shipmentType === 'sending'" class="w-full">
                                                            <div class="flex items-center gap-2">
                                                                <label for="sending" class="!w-full">
                                                                    <div class="!w-full flex justify-between">
                                                                        <div class="font-light">Envío a domicilio:</div>
                                                                        <div class="text-right font-semibold">€ {{ shipping.toFixed(2) }}</div>
                                                                    </div>
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div v-if="summary?.shipmentType === 'collection'" class="w-full">
                                                            <div class="flex items-center gap-2">
                                                                <label for="collection" class="!w-full">
                                                                    <div class="!w-full flex justify-between">
                                                                        <div class="font-light">Recoger en tienda:</div>
                                                                        <div class="text-right font-semibold">gratis</div>
                                                                    </div>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div v-if="summary.discount > 0" class="grid grid-cols-2 bg-red-100 p-1">
                                                        <div class="font-light text-red-600">
                                                            Descuento:
                                                        </div>
                                                        <div class="text-right font-semibold text-red-600">
                                                            € -{{ summary.discount.toFixed(2) }}
                                                        </div>
                                                    </div>

                                                    <div class="grid grid-cols-2">
                                                        <div class="font-light">
                                                            Subtotal:
                                                        </div>
                                                        <div class="text-right font-semibold">
                                                            € {{ summary.subtotal.toFixed(2) }}
                                                        </div>
                                                    </div>

                                                    <div class="grid grid-cols-2">
                                                        <div class="font-light">
                                                            Impuestos: {{ config.tax }}%
                                                        </div>
                                                        <div class="text-right font-semibold">
                                                            € {{ summary.tax.toFixed(2) }}
                                                        </div>
                                                    </div>

                                                    <div class="grid grid-cols-2 border-t border-slate-200 pt-4">
                                                        <div class="font-semibold">
                                                            Total:
                                                        </div>
                                                        <div class="text-right font-semibold">
                                                            € {{ useEnyesShop().roundTwo(summary.total).toFixed(2) }}
                                                        </div>
                                                    </div>
                                                </div>

                                                <Paypal v-if="cart && cart?.total && cart?.total > 0" :amount="useEnyesShop().roundTwo(summary.total)" :cart_id="cart.cartId" :invoice_id="cart.cartId" @success="successPayment" />

                                                <Divider align="center" class="!m-0">
                                                    <b>ó</b>
                                                </Divider>

                                                <AlternativePayment @data="alternativePayment" />
                                            </div>
                                        </div>
                                    </div>
                                    <div v-if="cart?.lines?.length > 0" class="flex pt-6">
                                        <Button size="small" @click="activateCallback('2'); step = '2'">
                                            <Icon name="akar-icons:arrow-left" /> Anterior
                                        </Button>
                                    </div>
                                </BlockUI>
                            </StepPanel>
                        </StepPanels>
                    </Stepper>
                </ClientOnly>
            </div>
        </div>
    </div>
</template>

<style lang="css" scoped>
    .p-tabpanels {
        @apply !p-0
    }
</style>
