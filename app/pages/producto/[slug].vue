<script setup lang="ts">
    import { type LineCart } from '../../utils/interfaces'

    interface Totals {
        units: number,
        total: number,
        fullTotal: number,
        unitary: number
    }

    const route = useRoute()
    const config = useInitialData().getConfig
    const slug: string = route.params?.slug as string
    const toast = useToast()
    const itemId: string = route.query?.itemId as string || ''
    const activeIndex = ref(0)
    const lightboxVisible = ref(false)
    const lightboxIndex = ref(0)
    const loadingProduct = ref(true)
    const colorSelected = ref<any>(null)
    const purchaseQuantity = ref<any>({})
    const itemLine = ref<LineCart | null>(null)
    const variants = ref<any>(null)
    const totals = ref<Totals>({
        units: 0,
        total: 0,
        fullTotal: 0,
        unitary: 0
    })
    const switchMarking = ref(false)
    const switchClicheRep = ref(false)
    const marking = ref<any>([])
    const printSelected = ref<any>(null)
    const prints: any = ref([])
    const areaSelected = ref<any>(null)
    const imagesForGallery = ref<any[]>([])
    const cartStore = useCartStore()
    const enyesShop = useEnyesShop()

    if (!slug) {
        navigateTo('/404')
    }

    const getPrintSelected = (printId: string) => {
        return prints.value.find((p: any) => p.print.id === printId)
    }

    const getAreaSelected = (printId: string, areaId: string) => {
        return getPrintSelected(printId).area.find((a: any) => a.id === areaId)
    }

    const markingPrices = computed(() => 
        enyesShop.calculateMarkingPrices(marking.value, totals.value.units)
    )

    const initPurchaseQuantity = (): any => {
        if (itemId) itemLine.value = cartStore.getLineById(itemId)
        if (itemLine.value?.slug && itemLine.value.slug !== slug) navigateTo('/404')

        const itemLineVariants: any = (itemLine.value) ? itemLine.value?.variants : {}
        marking.value = (itemLine.value) ? itemLine.value?.markings : []
        if (marking.value.length > 0 || itemLine.value?.includesMarking) switchMarking.value = true
        else switchMarking.value = false

        variants.value?.forEach((element: { id: string; }) => {
            const id: string = element.id
            const quantity = itemLineVariants[id]?.quantity || 0
            purchaseQuantity.value[id] = {
                ref: product.value?.id,
                product: `${ product.value?.name } (${ product.value?.id })`.trim(),
                variant: element,
                quantity
            }
        })
    }

    const { product, loading, error }: any = enyesShop.getProduct(slug)

    const openLightbox = () => {
        lightboxIndex.value = activeIndex.value
        lightboxVisible.value = true
    }

    const getUniqueColors = (variants: {
        color: { id: string; name: string; hexcode: string }, 
        id: string, 
        image: string,
        url_360?: string,
        legend: string,
        stock: number,
        price_1: number,
        limit_1: number,
        size: { id: string; name: string },
        viewsimages: string
    }[] = []) => {
        const uniqueColors = new Map<string, any>()

        variants.forEach(variant => { 
            const { color, id, image, url_360, legend, stock, price_1, limit_1, size, viewsimages } = variant
            if (!uniqueColors.has(color.id)) {
                uniqueColors.set(color.id, { 
                    ...color, 
                    variant_id: id, 
                    image,
                    url_360,
                    legend,
                    stock,
                    price_1,
                    limit_1,
                    size,
                    viewsimages
                })
            }
        })

        return Array.from(uniqueColors.values())
    }

    const uniqueColors = computed(() => {
        return getUniqueColors(variants.value || []) ?? []
    })

    const getVariantsByColor = (colorId: string) => {
        return variants.value?.filter((variant: any) => variant.color.id === colorId) ?? []
    }

    const getCategoryURL = (category: any) => {
        if (!category?.parent_id) {
            return `/catalogo/${ category?.slug }`
        } else {
            return `/catalogo?subcategories=${ category?.slug }`
        }
    }

    const deepClone = (obj: any) => {
        return JSON.parse(JSON.stringify(obj))
    }

    const addMarking = (print_id: string, area_id: string) => {
        const print = deepClone(prints.value.find((row: any) => row.print.id === print_id))
        if (!print) return
        const area = deepClone(print.area.find((area: any) => area.id === area_id))
        if (!area) return

        delete print.area

        const markingIndex = marking.value.findIndex((obj: any) => obj.print.print.id === print_id && obj.area.id === area_id)
        const newMarking = {
            id: `${print.print.id}-${area.id}`,
            print: print,
            area: area,
            clicheRep: switchClicheRep.value,
            marking: {
                description: `${ print.print.id } - ${ print.print.name } ${ (print.print.description) ? `(${ print.print.description })` : '' }`,
                complement: area.name
            }
        }

        if (markingIndex !== -1) {
            marking.value[markingIndex] = newMarking
        } else {
            marking.value.push(newMarking)
        }

        areaSelected.value = null
    }

    const deleteLineOfMarking = (print_id: string, area_id: string) => {
        marking.value = marking.value.filter((element: any) => !(element.print.id === print_id && element.area.id === area_id))
    }

    const onlyPurchaseQuantity = computed(() => {
        const purchase = Object.fromEntries(
            Object.entries(purchaseQuantity.value).filter(([key, item]: [string, any]) => item.quantity > 0)
        ) as Record<string, any>

        const units: number = Object.values(purchase).reduce((acc, item) => acc + item.quantity, 0)
        const total: number = Object.values(purchase).reduce((acc, item) => acc + (item.quantity * item.variant.price_1), 0)

        let markingTotal: number = enyesShop.markingTotal(marking.value, totals.value.units)

        const fullTotal: number = total + markingTotal
        const unitary: number = fullTotal / units || 0

        totals.value.units = units
        totals.value.total = total
        totals.value.fullTotal = enyesShop.roundTwo(fullTotal)
        totals.value.unitary = unitary

        return purchase
    })

    const addCart = () => {
        const productItem: LineCart = {
            id: (itemId) ? itemId : useTools().getRandomString(15),
            slug,
            product: `${ product.value.name } (${ product.value.id })`.trim(),
            variants: deepClone(onlyPurchaseQuantity.value),
            includesMarking: prints.value.length > 0 ? marking.value.length > 0 : switchMarking.value,
            markings: marking.value,
            quantity: totals.value.units,
            price: 0,
            subtotal: enyesShop.roundTwo(totals.value.total)
        }

        cartStore.addLine(productItem)

        if(itemId) {
            navigateTo(`/cart`)
        } else {
            marking.value = []
            initPurchaseQuantity()
            toast.add({ 
                severity: 'success', 
                summary: 'Agregado al carrito',
                life: 5000 })
        }
    }

    watch(
        () => ({
            product: product?.value ?? null,
            loading: loading?.value ?? true,
            error: error?.value ?? null
        }),
        ({ product, loading, error }) => {
            loadingProduct.value = loading
            if (!loading && (!product || error)) {
                navigateTo('/404')
            }

            if (product) {
                variants.value = product?.variants
                prints.value = product?.prints || []

                imagesForGallery.value = [
                    ...product.variants
                        .map((v: any) => v.image)
                        .filter((img: string | null | undefined) => !!img)
                ]

                colorSelected.value = uniqueColors.value[0]
                if (prints.value.length > 0) printSelected.value = prints.value[0].print.id

                initPurchaseQuantity()
            }
        },
        { deep: true }
    )
</script>

<template>
    <div>
        <Toast />
        <div v-if="loadingProduct === false" class="container w-full h-full mx-auto px-4 py-8 flex flex-col gap-6">
            <!-- ####  Info  #### -->
            <div class="w-full h-full grid md:grid-cols-2 gap-6">
                <!-- ####  Left  #### -->
                <div class="w-full !max-w-[612px] h-full flex flex-col gap-4">
                    <Galleria v-model:activeIndex="activeIndex" :value="imagesForGallery" :responsiveOptions="[
                        {
                            breakpoint: '1300px',
                            numVisible: 4
                        },
                        {
                            breakpoint: '575px',
                            numVisible: 3
                        }
                    ]" :numVisible="4" containerStyle="max-width: 612px;" :circular="true" :showItemNavigators="false" :showItemNavigatorsOnHover="false">
                        <template #item="slotProps">
                            <div class="w-full !max-w-[612px] !aspect-square bg-white overflow-hidden" @click="openLightbox">
                                <NuxtImg 
                                    :src="slotProps.item" 
                                    class="!w-full !h-full object-contain object-top" 
                                    quality="80" format="webp" width="612" 
                                    preload placeholder="/images/placeholder.webp"
                                />
                            </div>
                        </template>
                        <template #thumbnail="slotProps" class="!w-[90px] !h-[90px] !max-w-[90px] !max-h-[90px] !min-w-[90px] !min-h-[90px]">
                            <div class="md:!w-[90px] md:!h-[90px] md:!max-w-[90px] md:!max-h-[90px] md:!min-w-[90px] md:!min-h-[90px] !aspect-square overflow-hidden flex justify-center items-center">
                                <NuxtImg 
                                    :src="slotProps.item" 
                                    class="w-full h-full object-cover object-top mx-1" 
                                    quality="40" format="webp" width="90" height="90"
                                    preload placeholder="/images/empty_img.webp"
                                />
                            </div>
                        </template>
                    </Galleria>

                    <ClientOnly>
                        <vue-easy-lightbox
                            :visible="lightboxVisible"
                            :imgs="imagesForGallery.map((slide: any) => slide)"
                            :index="lightboxIndex"
                            @hide="lightboxVisible = false"
                        />
                    </ClientOnly>
                </div>

                <!-- ####  Right  #### -->
                <div class="w-full flex flex-col gap-2">
                    <div>
                        <Message severity="warn" class="rounded-lg">
                            <span class="text-sm font-light">Transporte e impuestos no incluidos. {{ config?.topBarMessage }}.</span>
                        </Message>
                    </div>

                    <!-- ####  Product Data  #### -->
                    <div>
                        <h1 class="text-2xl font-bold uppercase text-slate-800 josefin-title">{{ product?.name }} <span class="font-extralight">{{ product?.id }}</span></h1>
                        <div class="bg-primary-500 w-12 h-1 mt-1 mb-3"></div>
                        <p class="whitespace-pre-line text-sm font-light text-slate-500" v-html="product?.description"></p>
                    </div>

                    <!-- ####  Categories  #### -->
                    <div id="categories" v-if="product?.categories?.length > 0" class="text-base text-slate-500 flex flex-wrap gap-2">
                        <span class="font-semibold text-slate-600">Categorías Relacionadas:</span>
                        <template v-for="(item, i) in product?.categories" :key="item.category?.id" >
                            <div v-if="item.category !== null">
                                <NuxtLink :to="getCategoryURL(item.category)" 
                                    class="underline decoration-primary-600/80 underline-offset-4 hover:decoration-2 hover:text-slate-900 text-extralight"
                                >
                                    <span class="uppercase">{{ item.category?.name }}</span>
                                </NuxtLink>
                                <span v-if="i < product?.categories?.length - 1">, </span>
                                <span v-else>.</span>
                            </div>
                        </template>
                    </div>

                    <!-- ####  Colors  #### -->
                    <div id="colors" class="flex flex-wrap gap-6 md:gap-4 py-3 justify-center md:justify-normal">
                        <div v-for="color in uniqueColors" :key="color.id" v-tooltip.top="{ value: `${color.name}`, showDelay: 100 }" class="w-fit h-fit cursor-pointer" @click="() => colorSelected = color">
                            <div class="relative">
                                <div class="flex flex-col gap-1 justify-center items-center">
                                    <Colors :color="color" :class="{ '!border-slate-800 border-2 p-1': color.id === colorSelected.id }" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div v-if="colorSelected.url_360" class="my-2">
                        <NuxtLink :to="colorSelected.url_360" target="_blank" >
                            <Icon name="iconoir:view-360" size="30" />
                        </NuxtLink>
                    </div>

                    <!-- ####  Price Table  #### -->
                    <div class="flex flex-col gap-1">
                        <div>
                            <div class="text-xl font-semibold text-slate-800">Precio desde</div>
                            <div class="bg-primary-500 w-12 h-1 mt-1 mb-3"></div>
                        </div>

                        <div class="flex items-center gap-4">
                            <div class="w-fit h-fit px-8 py-2 rounded-md flex flex-col items-center text-white font-semibold bg-red-700">
                                <div class="text-xl">{{ product?.variants[0]?.price_1 }} €</div>
                            </div>
                        </div>
                        <span class="text-xs text-red-600 font-medium">* Impuestos no incluidos</span>
                    </div>

                    <!-- ####  Information  #### -->
                    <div class="max-w-[calc(100vw-30px)] mt-8 text-slate-800 border-b pb-4">
                        <Tabs value="0" scrollable>
                            <TabList>
                                <Tab value="0">Información</Tab>
                                <Tab value="1">Marcaje</Tab>
                                <Tab value="2">Packaging</Tab>
                            </TabList>

                            <TabPanels>
                                <TabPanel value="0">
                                    <div id="description" class="w-full h-full px-2 pt-2 text-sm text-slate-600">
                                        <div class="w-full h-full flex flex-col gap-2">
                                            <p class="font-light whitespace-pre-line">
                                                <span class="font-semibold">Material:</span> {{ product?.material }}
                                            </p>
                                        </div>
                                        <div v-if="product?.url_montaje" class="flex mt-4">
                                            <NuxtLink target="_blank" :to="product?.url_montaje">
                                                <Button severity="contrast" variant="outlined" class="w-fit">
                                                    <div class="flex items-center gap-1">
                                                        <Icon name="mingcute:t-shirt-line" size="20" />
                                                        <span class="font-light">Crear Montaje</span>
                                                    </div>
                                                </Button>
                                            </NuxtLink>
                                        </div>
                                    </div>
                                </TabPanel>
                                <TabPanel value="1">
                                    <div v-if="prints?.length > 0" id="marking" class="w-full h-full px-2 pt-2 flex flex-col gap-2">
                                        <FloatLabel class="w-full my-4" variant="on">
                                            <Select v-model="printSelected" inputId="on_label" :options="prints" option-label="print.name" option-value="print.id" class="w-full">
                                                <template #value="slotProps">
                                                    <div>{{ getPrintSelected(slotProps.value).print.name }} {{ getPrintSelected(slotProps.value).print.description }}</div>
                                                </template>

                                                <template #option="slotProps">
                                                    <div>{{ slotProps.option.print.name }} {{ slotProps.option.print.description }}</div>
                                                </template>
                                            </Select>

                                            <label for="on_label">Seleccione el marcaje</label>
                                        </FloatLabel>

                                        <div v-if="printSelected" class="w-full h-full flex flex-col gap-2">
                                            <div class="w-full flex flex-col items-center gap-2">
                                                <h3 class="text-lg font-semibold">{{ getPrintSelected(printSelected)?.print.id }} - {{ getPrintSelected(printSelected)?.print.name }}</h3>
                                                <h2 v-if="getPrintSelected(printSelected)?.print.description">{{ getPrintSelected(printSelected)?.print.description }}</h2>
                                                <div class="bg-primary-500 w-12 h-1"></div>
                                            </div>

                                            <div class="w-full grid grid-cols-1 md:grid-cols-2 gap-2">
                                                <div v-for="area in getPrintSelected(printSelected).area" :key="area.id" class="flex items-center gap-1">
                                                    <NuxtImg 
                                                        :src="area.image" 
                                                        class="w-40 h-40 object-cover" 
                                                        quality="40" format="webp" width="112" height="112" 
                                                        preload placeholder="/images/empty_img.webp"
                                                    />

                                                    <div class="flex flex-col text-xs">
                                                        <div class="font-semibold">{{ area.name }}</div>
                                                        <div>{{ area.measures }}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div v-else class="w-full h-full p-8 flex justify-center items-center gap-2">
                                        <WhatsappButton /> <span class="text-xl">Comuniquese con nosotros para más información.</span>
                                    </div>
                                </TabPanel>
                                <TabPanel value="2">
                                    <div class="w-full h-full px-2 pt-2 text-sm font-light flex flex-col gap-2">
                                        <div class="flex flex-col gap-2 justify-start">
                                            <div>
                                                <div v-if="product.weight"><span class="font-semibold">Peso caja: </span>{{ product.weight.toFixed(2) }} KG</div>
                                                <div v-if="product.outer_box_unit"><span class="font-semibold">Unidades caja exterior: </span>{{ product.outer_box_unit }}</div>
                                                <div v-if="product.inner_box_unit"><span class="font-semibold">Unidades caja interior: </span>{{ product.inner_box_unit }}</div>
                                                <div v-if="product.depth && product.width && product.height"><span class="font-semibold">Medidas caja exterior (Largo x Ancho x Alto): </span>{{ product.depth }} x {{ product.width }} x {{ product.height }} cm</div>
                                                <div v-if="product.measure_article"><span class="font-semibold">Medidas artículo: </span>{{ product.measure_article }}</div>
                                            </div>
                                        </div>
                                    </div>
                                </TabPanel>
                            </TabPanels>
                        </Tabs>
                    </div>
                </div>
            </div>

            <Divider />

            <div class="flex flex-col mb-[-14px]">
                <div class="text-lg font-semibold text-slate-800">Seleccione cantidad por color</div>
                <div class="bg-primary-500 w-12 h-1 mt-1 mb-3"></div>
            </div>

            <!-- ####  Sale  #### -->
            <div 
                v-if="colorSelected && variants && variants?.length > 0" 
                id="shop" class="w-full h-full grid md:grid-cols-[1.5fr_1fr] gap-4 md:gap-0 border"
            >
                <div class="w-full px-0 md:px-0 border-r-0 md:border-r">
                    <div class="w-full p-4 flex flex-wrap gap-2">
                        <div v-for="color in uniqueColors" :key="color.id" v-tooltip.top="{ value: `${color.name}`, showDelay: 100 }" class="w-fit h-fit cursor-pointer mt-4" @click="() => colorSelected = color">
                            <div class="relative">
                                <div class="flex flex-col gap-1 justify-center items-center">
                                    <Colors :color="color" :class="{ '!border-slate-800 border-2 p-1': color.id === colorSelected.id }" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="w-full px-4 py-8 border-b">
                        <div class="text-xl font-bold text-slate-800 josefin-title">{{ product?.name }}</div>
                        <div class="w-full flex items-center gap-2">
                            <div class="w-fit h-fit">
                                <div class="flex flex-col gap-1 items-center">
                                    <Colors :color="colorSelected" />
                                </div>
                            </div>
                            <span class="font-semibold">{{ colorSelected.name }}</span>
                        </div>
                    </div>

                    <div>
                        <div class="md:grid-cols-5 gap-2 p-2 border-b hidden md:grid">
                            <p class="font-semibold flex justify-center items-center">CANTIDAD</p>
                            <p class="font-semibold flex justify-center items-center">TALLA</p>
                            <p class="font-semibold flex justify-center items-center">PRECIO</p>
                            <p class="font-semibold flex justify-center items-center">STOCK</p>
                            <p class="font-semibold flex justify-center items-center">DISPONIBILIDAD</p>
                        </div>

                        <div
                            v-for="variant in getVariantsByColor(colorSelected.id)" :key="variant.id"
                            class="grid md:grid-cols-5 gap-2 border-b text-sm odd:bg-gray-100 even:bg-white"
                            :class="[{ 'p-2': variant.stock > 0 || variant.canteco > 0 }, { 'py-4': variant.stock === 0 }]"
                        >
                            <!-- ####  Rows  #### -->
                            <div class="flex gap-2 justify-between md:justify-center items-center">
                                <p class="md:hidden font-semibold">Cantidad:</p>
                                <div v-if="variant.stock > 0" class="w-full px-2 md:p-0">
                                    <InputNumber 
                                        v-model="purchaseQuantity[variant.id].quantity" 
                                        showButtons 
                                        fluid
                                        buttonLayout="horizontal" 
                                        :step="1" 
                                        size="small"
                                        class="text-center"
                                        :min="0" :max="variant.stock"
                                        :locale="'es'"
                                        :useGrouping="false"
                                    >
                                        <template #incrementicon>
                                            <Icon name="akar-icons:plus" />
                                        </template>
                                        <template #decrementicon>
                                            <Icon name="akar-icons:minus" />
                                        </template>
                                    </InputNumber>
                                </div>
                            </div>
                            <div class="flex gap-1 justify-start md:justify-center items-center">
                                <p class="md:hidden font-semibold">Talla:</p>
                                <div v-if="variant?.size?.name" class="flex flex-col items-center">
                                    <span>{{ variant.size.name }}</span>
                                </div>
                            </div>
                            <div class="flex gap-1 justify-start md:justify-center items-center">
                                <p class="md:hidden font-semibold">Precio:</p>
                                <span>{{ variant.price_1 }} €</span>
                            </div>
                            <div class="flex gap-1 justify-start md:justify-center items-center">
                                <p class="md:hidden font-semibold">Stock:</p>
                                <p>{{ variant.stock }}</p>
                            </div>
                            <div class="flex gap-1 justify-start md:justify-center items-center">
                                <p class="md:hidden font-semibold">Disponibilidad:</p>
                                <p v-if="variant.stock > 0">En stock</p>
                                <p v-else>Sin stock</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="p-4 flex flex-col gap-8">
                    <div v-if="totals.units > 0" class="flex flex-col justify-start gap-4">
                        <div class="flex items-center gap-3 border rounded-md px-4 py-2">
                            <ToggleSwitch v-model="switchMarking" @change="() => { areaSelected = null; switchClicheRep = false; marking = []; }" />
                            <span class="font-semibold">¿Incluir Marcaje?</span>
                        </div>

                        <div v-if="prints?.length > 0" v-show="switchMarking" class="flex flex-col gap-4 transition-all duration-300 ease-in-out">
                            <div class="flex items-center gap-3 border rounded-md px-4 py-2">
                                <ToggleSwitch v-model="switchClicheRep" @change="() => { areaSelected = null; }" />
                                <span class="font-semibold">¿Repiten trabajos? (Repetir Cliché)</span>
                            </div>

                            <div class="flex flex-col gap-2 border rounded-md p-2">
                                <div class="w-full font-semibold text-center">Seleccione el marcaje, y el área que desea.</div>
                                <Select v-model="printSelected" inputId="on_label" :options="prints" option-label="print.name" option-value="print.id" class="w-full" placeholder="Seleccione" @change="() => areaSelected = null">
                                    <template #value="slotProps">
                                        <div>{{ getPrintSelected(slotProps.value).print.name }} {{ getPrintSelected(slotProps.value).print.description }}</div>
                                    </template>

                                    <template #option="slotProps">
                                        <div>{{ slotProps.option.print.name }} {{ slotProps.option.print.description }}</div>
                                    </template>
                                </Select>

                                <Select v-model="areaSelected" :options="getPrintSelected(printSelected)?.area || []" option-label="name" option-value="id" placeholder="Seleccione">
                                    <template #value="slotProps">
                                        <div v-if="!slotProps.value">
                                            Seleccione
                                        </div>
                                        <div v-else>
                                            {{ getAreaSelected(printSelected, slotProps.value).name }} 
                                            {{ getAreaSelected(printSelected, slotProps.value).measures }}
                                        </div>
                                    </template>

                                    <template #option="slotProps">
                                        <div>{{ slotProps.option.name }} {{ slotProps.option?.measures }}</div>
                                    </template>
                                </Select>
                            </div>

                            <Button v-if="areaSelected !== null" @click="() => { addMarking(printSelected, areaSelected) }">Agregar Marcaje</Button>
                        </div>
                        <div v-else-if="switchMarking">
                            <Message severity="info">Para saber el costo del marcaje debes contactarnos.</Message>
                        </div>

                        <Divider />
                    </div>

                    <div>
                        <div class="flex justify-between text-2xl font-semibold">
                            <div>Total:</div>
                            <div>{{ totals.fullTotal.toFixed(2) }} €</div>
                        </div>

                        <div class="flex justify-between text-base">
                            <div>Precio Unitario:</div>
                            <div>{{ totals.unitary.toFixed(3) }} €</div>
                        </div>

                        <div class="flex justify-between text-base">
                            <div>Unidades:</div>
                            <div>{{ totals.units }}</div>
                        </div>

                        <Divider />

                        <div class="">
                            <div v-for="(PurchasedVariant, id, i) in onlyPurchaseQuantity" :key="id" class="pb-4 border-b" :class="{ 'pt-4': (i > 0) }">
                                <div class="font-semibold">{{ PurchasedVariant.product }}</div>
                                <div class="text-xs">{{ id }} <span v-if="PurchasedVariant.variant.legend">- {{ PurchasedVariant.variant.legend }}</span></div>
                                <div class="text-xs">{{ PurchasedVariant.quantity }} unidades x {{ PurchasedVariant.variant.price_1 }} €</div>
                            </div>

                            <div v-for="(mark) in marking" :key="`${ mark.print.print.id }-${ mark.area.id }`">
                                <div class="pb-4 pt-4 border-b">
                                    <div class="flex w-full">
                                        <div class="w-2/3">
                                            <div class="font-semibold flex justify-left items-center gap-1">
                                                <span>{{ mark.marking.description }}</span><span v-if="mark.clicheRep">-</span>
                                                <span v-if="mark.clicheRep" class="text-xs font-semibold text-orange-600"> REPETICIÓN</span>
                                            </div>
                                            <div class="text-xs">{{ mark.marking.complement }}</div>

                                            <div v-if="markingPrices[`${mark.print.print.id}-${mark.area.id}`].minJob" class="text-xs">Se aplica trabajo mínimo.</div>
                                            <div v-else class="text-xs">{{ totals.units }} unidades x {{ markingPrices[`${mark.print.print.id}-${mark.area.id}`].price }} €</div>
                                        </div>
                                        <div class="w-1/3 flex justify-end items-center">
                                            <Button text @click="deleteLineOfMarking(mark.print.id, mark.area.id)">
                                                <Icon name="ic:baseline-delete-outline" class="text-red-600" size="25" />
                                            </Button>
                                        </div>
                                    </div>
                                </div>

                                <div class="py-4 border-b">
                                    <div class="w-full flex flex-col">
                                        <div class="font-semibold"> Puesta en máquina </div>
                                        <div class="text-xs"> 1 x {{ mark.clicheRep ? (mark.print.print.clicherep / 1000).toFixed(2) : (mark.print.print.cliche / 1000).toFixed(2) }} € </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div v-if="totals.units > 0 && totals.units < product.minimal_quantity" class="w-full mt-4">
                            <Message severity="warn">
                                <span class="font-light">La orden mínima para este producto es de {{ product.minimal_quantity }} unidades.</span>
                            </Message>
                        </div>
                        <div v-else-if="totals.units > 0" class="w-full mt-4 flex justify-end">
                            <Button :severity="itemId ? 'info' : 'success'" @click="addCart"><span class="font-semibold">{{ itemId ? 'Actualizar Carrito' : 'Agregar al Carrito' }}</span></Button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ####  Loading Skeleton  #### -->
        <div v-else class="container w-full h-full mx-auto px-4 py-8 flex flex-col gap-6">
            <div>
                <div class="w-full h-full grid md:grid-cols-2 gap-6">
                    <div class="w-full !max-w-[612px] h-full flex flex-col gap-4">
                        <div class="w-full !max-w-[612px] h-[300px] md:h-[500px] max-h-[612px] flex justify-center items-start">
                            <Skeleton height="500px" width="100%" class="hidden md:block" />
                            <Skeleton height="300px" width="100%" class="md:hidden" />
                        </div>

                        <div class="!h-[116px] max-!h-[116px]" content-class="max-w-scroll md:w-full !h-full flex items-center gap-1">
                            <Skeleton height="116px" width="100%" class="w-full h-full object-cover" />
                        </div>
                    </div>

                    <div class="w-full flex flex-col gap-2">
                        <div class="w-full h-24">
                            <Skeleton height="96px" width="100%" class="w-full h-full object-cover" />
                        </div>

                        <div class="w-full flex flex-col gap-2">
                            <div>
                                <Skeleton height="24px" width="96px" />
                            </div>
                            <div>
                                <div>
                                    <Skeleton height="34px" width="300px" />
                                </div>
                                <div class="bg-slate-300 w-12 h-0.5 my-5"></div>
                            </div>

                            <div class="flex gap-2">
                                <Skeleton height="20px" width="80px" />
                            </div>
                            <div class="flex gap-2">
                                <Skeleton height="20px" width="85%" />
                            </div>
                            <div class="flex gap-2">
                                <Skeleton height="20px" width="80%" />
                            </div>

                            <div class="flex flex-wrap gap-2">
                                <Skeleton height="20px" width="120px" />
                                <Skeleton height="20px" width="125px" />
                                <Skeleton height="20px" width="120px" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
