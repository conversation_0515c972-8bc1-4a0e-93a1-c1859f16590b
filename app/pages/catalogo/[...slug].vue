<script setup lang="ts">
    interface Filter {
        categoriesSelected: any
        subcategoriesSelected: any
        colorSelected: any
        materialSelected: any
        // sizeSelected: any
        sort: any
    }

    interface RouteOptionsType {
        path: string
        query?: {
            categories?: string
            subcategories?: string
            colors?: string
            materials?: string
            // sizes?: string
            search?: string
        };
    }

    const route = useRoute()
    const collapsed = ref(false)
    const isMobile = ref(false)
    const page = ref(0)
    const hideLoadMore = ref(true)
    const search = ref<any>('')
    const materials = ref<string[]>([])

    const categoriesFilter = ref<Array<any>>([])
    const subcategoriesFilter = ref<Array<any>>([])
    const colorsFilter = ref<Array<any>>([])
    const materialsFilter = ref<Array<any>>([])
    // const sizesFilter = ref<Array<any>>([])
    const sortFilter = ref<any>({ name: 'asc' })

    const filter = ref<Filter>({
        categoriesSelected: [],
        subcategoriesSelected: [],
        colorSelected: [],
        materialSelected: [],
        // sizeSelected: [],
        sort: sortFilter.value
    })

    const checkRoute = () => {
        if (route.query?.search) {
            search.value = route.query?.search
        } else {
            search.value = ''
        }

        if (route?.params && route.params?.slug && Array.isArray(route.params.slug)) {
            categoriesFilter.value = route.params.slug
        } else if(route.query?.categories) {
            categoriesFilter.value = route.query.categories.toLocaleString().split(',')
        } else {
            categoriesFilter.value = []
        }

        if (route.query?.subcategories) {
            subcategoriesFilter.value = route.query.subcategories.toLocaleString().split(',')
        } else {
            subcategoriesFilter.value = []
        }

        if (route.query?.colors) {
            colorsFilter.value = route.query.colors.toLocaleString().split(',')
        } else {
            colorsFilter.value = []
        }

        if (route.query?.materials) {
            materialsFilter.value = route.query.materials.toLocaleString().split(',')
        } else {
            materialsFilter.value = []
        }

        // if (route.query?.sizes) {
        //     sizesFilter.value = route.query.sizes.toLocaleString().split(',')
        // } else {
        //     sizesFilter.value = []
        // }

        filter.value = {
            categoriesSelected: categoriesFilter.value,
            subcategoriesSelected: subcategoriesFilter.value,
            colorSelected: colorsFilter.value,
            materialSelected: materialsFilter.value,
            // sizeSelected: sizesFilter.value,
            sort: sortFilter.value
        }
    }

    const updateSidebar = () => {
        if (window.innerWidth <= 428) {
            collapsed.value = true
            isMobile.value = true
        }
    }

    const updateURL = () => {
        let path: string = `/catalogo`
        let query: any = {}

        if (categoriesFilter.value.length === 1) {
            path += `/${ categoriesFilter.value[0] }`
        } else if (categoriesFilter.value.length > 1) {
            query.categories = categoriesFilter.value.join(',')
        }

        if (subcategoriesFilter.value.length > 0) {
            query.subcategories = subcategoriesFilter.value.join(',')
        }

        if (colorsFilter.value.length > 0) {
            query.colors = colorsFilter.value.join(',')
        }

        if (materialsFilter.value.length > 0) {
            query.materials = materialsFilter.value.join(',')
        }

        // if (sizesFilter.value.length > 0) {
        //     query.sizes = sizesFilter.value.join(',')
        // }

        if (search.value) {
            query.search = search.value
        }

        const routeOptions: RouteOptionsType = {
            path,
            query
        }

        navigateTo(routeOptions)
    }

    const updateFilters = () => {
        page.value = 0
        updateURL()
    }

    const updateCategory = (category: any, type: string) => {
        materialsFilter.value = []
        if (type === 'category') {
            const isSelected = categoriesFilter.value.includes(category.slug)
            if (isSelected) {
                category.subcategories.forEach((sub: any) => {
                    if (!subcategoriesFilter.value.includes(sub.slug)) {
                        subcategoriesFilter.value.push(sub.slug)
                    }
                })
            } else {
                category.subcategories.forEach((sub: any) => {
                    subcategoriesFilter.value = subcategoriesFilter.value.filter(s => s !== sub.slug)
                })
            }
        } else if (type === 'subcategory') {
            const allSelected = category.subcategories.every((sub: any) => subcategoriesFilter.value.includes(sub.slug))
            if (allSelected) {
                if (!categoriesFilter.value.includes(category.slug)) {
                    categoriesFilter.value.push(category.slug)
                }
            } else {
                categoriesFilter.value = categoriesFilter.value.filter(c => c !== category.slug)
            }
        }
        updateFilters()
    }

    const updateColor = (color: string) => {
        const index = colorsFilter.value.indexOf(color)
        if (index !== -1) {
            colorsFilter.value.splice(index, 1)
        } else {
            colorsFilter.value.push(color)
        }
        updateFilters()
    }

    const updateMaterial = (material: string) => {
        const index = materialsFilter.value.indexOf(material)
        if (index !== -1) {
            materialsFilter.value.splice(index, 1)
        } else {
            materialsFilter.value.push(material)
        }
        updateFilters()
    }

    const { categories, loadingCategories }: any = useEnyesShop().getCategories()
    const { colors, loadingColors }: any = useEnyesShop().getColors()
    // const { sizes, loadingSizes }: any = useEnyesShop().getSizes()

    watch(
        () => [route.params.slug, route.query.categories, route.query.subcategories, route.query.colors, route.query.search, route.query.materials],
        () => {
            checkRoute()
        },
        { immediate: true }
    )

    if (import.meta.client) {
        updateSidebar()
    }
</script>

<template>
    <div class="container w-full h-full mx-auto px-4 my-4">
        <div class="w-full h-full flex">
            <!-- Sidebar -->
            <div v-show="!collapsed" class="block w-[290px] transition-all duration-300 ease-in-out border-r">
                <div class="flex flex-col flex-1 overflow-y-auto">
                    <div class="w-full px-2"><SearchComponent /></div>
                    <nav class="flex-1 px-2 py-4">
                        <!-- ####  Categories  #### -->
                        <!-- ------------------------------------------------------ -->
                        <div class="w-full pr-3 pb-6 border-b">
                            <div class="py-3 font-semibold text-lg flex justify-between">
                                <div>
                                    <h4>Categorías</h4>
                                </div>
                                <div v-if="!collapsed && isMobile">
                                    <Icon name="akar-icons:x-small" class="w-7 h-7 text-red-600 cursor-pointer" @click="() => collapsed = !collapsed" />
                                </div>
                            </div>
                            <ScrollPanel style="width: 100%; max-height: 400px; height: 400px">
                                <div v-if="!loadingCategories" class="h-full flex flex-col gap-6 pb-6">
                                    <div class="flex flex-col gap-3" v-if="categories?.length > 0" v-for="category in categories" :key="category.id">
                                        <div class="flex items-center gap-2">
                                            <Checkbox :inputId="category.slug" v-model="categoriesFilter" :value="category.slug" @change="updateCategory(category, 'category')" />
                                            <label :for="category.slug" class="text-principal-800 font-semibold"> {{ category.name.toUpperCase() }} </label>
                                        </div>

                                        <div v-if="category.subcategories.length > 0" class="w-full h-full flex flex-col gap-3 pl-6">
                                            <div v-for="subcategory in category.subcategories" :key="subcategory.id" class="flex items-center gap-2">
                                                <Checkbox :inputId="subcategory.slug" v-model="subcategoriesFilter" :value="subcategory.slug" @change="updateCategory(category, 'subcategory')" />
                                                <label :for="subcategory.slug" class="capitalize"> {{ subcategory.name }} </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div v-else class="w-full h-full flex justify-center items-center">
                                        <AxEmpty message="Sin Categorías" />
                                    </div>
                                    <div class="min-h-[12px] h-[12px]"></div>
                                </div>
                                <div v-else class="w-full h-full flex justify-center">
                                    <ProgressSpinner style="width: 50px; height: 50px" />
                                </div>
                            </ScrollPanel>
                        </div>
                        <!-- ------------------------------------------------------ -->
                        <!-- ####  End Categories  #### -->

                        <!-- ####  Colors  #### -->
                        <!-- ------------------------------------------------------ -->
                        <div class="w-full pr-3 pb-6 border-b">
                            <div class="py-3 font-semibold text-lg">
                                <h4>Colores</h4>
                            </div>
                            <ScrollPanel style="width: 100%; max-height: 300px; height: 300px">
                                <div v-if="!loadingColors" class="flex grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2">
                                    <div v-if="colors?.length > 0" v-for="color in colors" @click="updateColor(color.id)" :key="color.id" v-tooltip.top="{ value: `${color.name}`, showDelay: 100 }" class="flex flex-col gap-1 items-center relative hover:cursor-pointer">
                                        <div class="relative">
                                            <Colors :color="color" />
                                        </div>

                                        <svg v-if="colorsFilter.includes(color.id)" class="absolute top-1 left-1.5 w-6 h-6" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24">
                                            <path fill="#fa0810" stroke="#000000" stroke-width="1" d="M9 20.42L2.79 14.21l2.83-2.83L9 14.77l9.88-9.89l2.83 2.83z"/>
                                        </svg>
                                    </div>
                                    <div v-else class="w-full flex justify-center items-center">
                                        <AxEmpty message="Sin Colores" />
                                    </div>
                                </div>
                                <div v-else class="w-full flex justify-center">
                                    <ProgressSpinner style="width: 50px; height: 50px" />
                                </div>
                            </ScrollPanel>
                        </div>
                        <!-- ------------------------------------------------------ -->
                        <!-- ####  End Colors  #### -->

                        <!-- ####  Materials  #### -->
                        <!-- ------------------------------------------------------ -->
                        <div class="w-full pr-3 pb-6 border-b">
                            <div class="py-3 font-semibold text-lg">
                                <h4>Materiales</h4>
                            </div>
                            <ScrollPanel style="width: 100%; max-height: 400px; height: 400px">
                                <div v-if="materials.length > 0" class="flex flex-col gap-2">
                                    <div v-for="material in materials" :key="material" class="flex items-center gap-2">
                                        <Checkbox :inputId="'material-' + material" v-model="filter.materialSelected" :value="material" @change="updateMaterial(material)" />
                                        <label :for="'material-' + material" class="capitalize">{{ material }}</label>
                                    </div>
                                </div>
                                <div v-else class="w-full flex justify-center items-center">
                                    <AxEmpty message="Sin Materiales" />
                                </div>
                            </ScrollPanel>
                        </div>
                        <!-- ------------------------------------------------------ -->
                        <!-- ####  End Materials  #### -->

                        <!-- ####  Sizes  #### -->
                        <!-- ------------------------------------------------------ -->
                        <!-- <div class="w-full pr-3 pb-6 border-b">
                            <div class="py-3 font-semibold text-lg">
                                <h4>Tallas</h4>
                            </div>
                            <ScrollPanel style="width: 100%; max-height: 400px; height: 400px">
                                <div v-if="!loadingSizes" class="w-full grid grid-cols-2 gap-4">
                                    <div class="flex items-center gap-2" v-if="sizes.length > 0" v-for="size in sizes" :key="size.id">
                                        <Checkbox :inputId="size.id" v-model="sizesFilter" :value="size.id" @change="updateFilters" />
                                        <label class="text-md md:text-xs font-semibold" :for="size.id"> {{ size.name }} </label>
                                    </div>
                                    <div v-else class="w-full h-full flex justify-center items-center">
                                        <AxEmpty message="Sin Tallas" />
                                    </div>
                                </div>
                                <div v-else class="w-full flex justify-center">
                                    <ProgressSpinner style="width: 50px; height: 50px" />
                                </div>
                            </ScrollPanel>
                        </div> -->
                        <!-- ------------------------------------------------------ -->
                        <!-- ####  End Sizes  #### -->
                    </nav>
                </div>
            </div>

            <!-- Main content -->
            <div class="w-full flex flex-col flex-1">
                <h1 class="text-3xl font-bold text-slate-800 text-center md:text-left md:ml-4 mb-4 josefin-title">Catálogo de Productos</h1>
                <div class="w-full h-full flex flex-col gap-4">
                    <div class="w-full min-w-full flex justify-between items-center">
                        <div class="w-9 h-9 flex justify-center items-center bg-primary-700 hover:bg-primary-800 rounded-md p-1.5 cursor-pointer ml-2" @click="() => collapsed = !collapsed">
                            <Icon name="carbon:filter" class="w-7 h-7 text-white" />
                        </div>

                        <div class="w-full flex justify-end block mr-2">
                            <Select v-model="sortFilter" class="w-40 right-0" :options="
                                [
                                    {
                                        label: 'Nombre [A - Z]',
                                        value: { name: 'asc' }
                                    },
                                    {
                                        label: 'Nombre [Z - A]',
                                        value: { name: 'desc' }
                                    },
                                    {
                                        label: 'Menor valor',
                                        value: { variants_aggregate: { max: { price_1: 'asc' } } }
                                    },
                                    {
                                        label: 'Mayor valor',
                                        value: { variants_aggregate: { max: { price_1: 'desc' } } }
                                    }
                                ]" option-label="label" option-value="value" placeholder="Orden"
                                @change="(_val) => { filter.sort = _val.value; page = 0; }"
                            />
                        </div>
                    </div>

                    <div class="w-full">
                        <ProductFiltered :filter="filter" :page="page" :search="search" @is-complete="(complete: boolean) => hideLoadMore = complete" @materials="(resp: string[]) => materials = resp" />
                    </div>

                    <div class="w-full flex justify-center py-4">
                        <Button v-if="!hideLoadMore" @click="page += 1">
                            <span class="text-slate-50 font-semibold">
                                Mostrar más
                            </span>
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
