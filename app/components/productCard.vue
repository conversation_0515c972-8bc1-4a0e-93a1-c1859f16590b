<script setup lang="ts">
import { computed } from 'vue'

function stripHtml(html: string): string {
    if (!html) return ''
    return html.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ').trim()
}

const props = defineProps({
    product: {
        type: Object,
        required: true
    }
})

const cleanDescription = computed(() => stripHtml(props.product?.description))

const allimages = computed(() => {
    return props.product?.variants.map((v: any) => v.image).filter((v: any) => v)
})
</script>

<template>
    <NuxtLink :to="`/producto/${ product.slug }`" target="_blank">
        <div class="min-w-[300px] w-[300px] p-2 max-w-[300px] h-full flex flex-col justify-between bg-white mb-4 rounded-lg hover:cursor-pointer hover:shadow-lg border border-slate-100">
            <div class="w-full h-[300px] p-2">
                <NuxtImg 
                    :src="product.image" 
                    class="w-full h-full object-cover object-center rounded-lg" 
                    quality="60" format="webp" width="300" 
                    preload placeholder="/images/placeholder.webp"
                />
            </div>

            <div class="w-fit h-fit flex flex-col px-3 py-1 text-slate-800 text-sm bg-zinc-50 rounded-lg border border-zinc-100">
                <div v-tooltip.top="`${product.id} - ${product.name}`" class="w-fit font-semibold text-lg uppercase line-clamp-1">{{ product.name }}</div>
                <div class="text-xs text-gray-400 line-clamp-2">{{ cleanDescription }}</div>
                <div class="text-[15px] mt-1 flex flex-col">
                    <div class="text-sm text-slate-700 font-light">Precios desde</div>
                    <div class="flex justify-between items-center">
                        <div class="w-1/2" v-if="product?.variants[0]?.price_1">
                            <span class="text-lg font-semibold">{{ product?.variants[0]?.price_1 }} €</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </NuxtLink>
</template>
