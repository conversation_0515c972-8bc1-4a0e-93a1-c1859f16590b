<script setup lang="ts">
    const { loggedIn }: any = useUserSession();
    const props = defineProps({
        invoice_id: {
            type: String,
            required: true
        },
        cart_id: {
            type: String,
            required: true
        },
        amount: {
            type: Number,
            required: true
        }
    })

    const emit = defineEmits(['success', 'error']);

    const { $paypal } = useNuxtApp()
    const processing = ref(false)
    const message = ref('')
    const messageType = ref('info')

    const showError = (msg: string) => {
        message.value = msg
        messageType.value = 'error'
    }

    const showSuccess = (msg: string) => {
        message.value = msg
        messageType.value = 'success'
    }

    const createOrder = async () => {
        if (!loggedIn.value) {
            navigateTo(`/auth`);
            return;
        } else {
            try {
                processing.value = true
                message.value = 'Procesando pago...'
                messageType.value = 'info'

                const response: any = await $fetch('/api/payment/orders', {
                    method: 'POST',
                    body: { 
                        intent: "CAPTURE",
                        payment_source: {
                            paypal: { 
                                experience_context: { 
                                    brand_name: "EBP Publicidad",
                                    locale: "es-ES",
                                    payment_method_preference: "IMMEDIATE_PAYMENT_REQUIRED", 
                                    landing_page: "GUEST_CHECKOUT", 
                                    shipping_preference: "NO_SHIPPING", 
                                    user_action: "PAY_NOW"
                                } 
                            } 
                        }, 
                        purchase_units: [ 
                            { 
                                invoice_id: props.invoice_id,
                                reference_id: props.cart_id, 
                                amount: { 
                                    currency_code: "EUR", 
                                    value: props.amount.toString(),
                                },
                            } 
                        ] 
                    }
                })

                if (!response?.id) {
                    throw new Error('No se recibió el ID de la orden')
                }

                return response.id
            } catch (error: any) {
                showError('Error en el proceso de pago: ' + (error?.data?.data?.error || error.data?.statusMessage || error.data?.error || error.message))
                throw error
            } finally {
                processing.value = false
            }
        }
    }

    const onApprove = async (data: any) => {
        try {
            processing.value = true
            message.value = 'Finalizando pago...'
            messageType.value = 'info'

            const response: any = await $fetch(`/api/payment/orders/${data.orderID}`, {
                method: 'POST',
                query: {
                    action: 'capture'
                }
            })
            
            if (response?.status === 'COMPLETED') {
                showSuccess('¡Pago completado con éxito!')
                emit('success', response);
            } else {
                showError('Error al finalizar el pago: ' + response?.status)
                emit('error', response);
            }
        } catch (error: any) {
            showError('Error al finalizar el pago: ' + (error?.data?.data?.error || error.data?.statusMessage || error.data?.error || error.message))
            emit('error', error);
        } finally {
            processing.value = false
        }
    }

    onMounted(async () => {
        if (import.meta.server) return

        try {
            const paypal = await $paypal.load()
            if (!paypal) {
                throw new Error('PayPal SDK no se pudo cargar')
            }

            await paypal.Buttons({
                style: {
                    layout: 'vertical',
                    color: 'silver',
                    shape: 'rect',
                    label: 'paypal'
                },
                createOrder,
                onApprove,
                onCancel: () => {
                    message.value = 'Pago cancelado'
                    messageType.value = 'warn'
                },
                onError: (err: any) => {
                    showError('Error en el proceso de pago: ' + err.message)
                    emit('error', err)
                }
            }).render('#paypal-button-container')
        } catch (error: any) {
            showError('Error al inicializar el formulario de pago: ' + error.message)
            emit('error', error)
        }
    })
</script>

<template>
    <div class="payment-container w-full">
        <div class="payment-form w-full">
            <ClientOnly>
                <div v-if="message" :class="['message', messageType]" class="flex items-center justify-center gap-2">
                    <ProgressSpinner v-if="messageType === 'info'" class="!m-0" style="width: 20px; height: 20px" strokeWidth="5" fill="transparent" animationDuration="3s" aria-label="Custom ProgressSpinner" />
                    <div>{{ message }}</div>
                </div>
                <div id="paypal-button-container" class="my-4 paypal-button-container"></div>
            </ClientOnly>
        </div>
    </div>
</template>

<style lang="css" scoped>
    .payment-container {
        max-width: 100%;
        margin: 0 auto;
    }

    .payment-form {
        background: white;
    }

    .message {
        margin-top: 1rem;
        padding: 1rem;
        border-radius: 4px;
        text-align: center;
    }

    .message.error {
        background-color: #ffebee;
        color: #c62828;
        border: 1px solid #ffcdd2;
    }

    .message.success {
        background-color: #e8f5e9;
        color: #2e7d32;
        border: 1px solid #c8e6c9;
    }

    .message.info {
        background-color: #e3f2fd;
        color: #1565c0;
        border: 1px solid #bbdefb;
    }

    .message.warn {
        background-color: #fff3e0;
        color: #b18500;
        border: 1px solid #ffd8a8;
    }

    :deep(.component-frame) {
        outline: none;
    }
</style>
