<script setup lang="ts">
    import { navigateTo } from '#app'

    const cartStore = useCartStore();
    const cartCount = computed(() => cartStore.cart.quantityTotal);
</script>

<template>
    <div>
        <div class="w-fit cursor-pointer flex justify-center items-center" @click="() => navigateTo('/cart')">
            <div class="relative inline-flex">
                <Icon name="akar-icons:shopping-bag" size="26" />
                <OverlayBadge v-show="cartCount !== 0" severity="warn" size="small" :unstyled="true" class="absolute -top-2 -right-2">
                    <template #default>
                        <div class="w-[10px] h-[10px] flex items-center justify-center text-[11px] bg-orange-600 text-white rounded-full p-[10px]">
                            <ClientOnly >
                                {{ cartCount }}
                            </ClientOnly>
                        </div>
                    </template>
                </OverlayBadge>
            </div>
        </div>
    </div>
</template>
