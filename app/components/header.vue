<script lang="ts" setup>
    const config = useInitialData().getConfig
    const route = useRouter()
</script>

<template>
    <section v-if="config.img1 && config.img2 && config.img3 && config.img4" class="container mx-auto px-2 md:px-8 py-4">
        <div class="w-full h-full flex flex-col md:flex-row gap-3">
            <div @click="config.link1 && route.push(config.link1)" :class="['w-full md:w-1/2 h-[550px] bg-cover bg-center flex relative', config.link1 ? 'cursor-pointer' : '']" :style="{ backgroundImage: `url(${ config.img1 })` }">
                <span
                  class="absolute text-primary-800 text-2xl lg:text-4xl font-extrabold left-3 top-2 russo-one drop-shadow-lg"
                  style="text-shadow: 1px 1px 4px #94a3b8, 0 1px 0 #94a3b8;"
                >
                  {{ config?.text1 }}
                </span>
            </div>
            <div class="w-full md:w-1/2 h-[550px] flex flex-col gap-3">
                <div @click="config.link2 && route.push(config.link2)" :class="['w-full h-1/2 flex-1 flex bg-primary-800 bg-cover bg-center relative', config.link2 ? 'cursor-pointer' : '']" :style="{ backgroundImage: `url(${ config.img2 })` }">
                    <span class="absolute text-white text-2xl lg:text-4xl font-extrabold drop-shadow-lg left-3 bottom-2 russo-one"
                          style="letter-spacing: 1px; text-shadow: 1px 1px 4px #94a3b8, 0 1px 0 #94a3b8;">
                      {{ config?.text2 }}
                    </span>
                </div>
                <div class="w-full h-1/2 flex gap-3 flex-1">
                    <div @click="config.link3 && route.push(config.link3)" :class="['w-1/2 h-full flex items-center justify-center bg-cover bg-center relative', config.link3 ? 'cursor-pointer' : '']" :style="{ backgroundImage: `url(${ config.img3 })` }">
                        <span class="absolute text-2xl font-extrabold drop-shadow-lg bottom-2 russo-one block w-full truncate px-3"
                              style="letter-spacing: 1px; text-shadow: 1px 1px 4px #94a3b8, 0 1px 0 #94a3b8;">
                          {{ config?.text3 }}
                        </span>
                    </div>
                    <div @click="config.link4 && route.push(config.link4)" :class="['w-1/2 h-full flex items-center justify-center bg-cover bg-center relative', config.link4 ? 'cursor-pointer' : '']" :style="{ backgroundImage: `url(${ config.img4 })` }">
                        <span class="absolute text-2xl font-extrabold drop-shadow-lg left-0 bottom-2 russo-one block w-full truncate px-3"
                              style="letter-spacing: 1px; text-shadow: 1px 1px 4px #94a3b8, 0 1px 0 #94a3b8;">
                          {{ config?.text4 }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </section>
</template>
