<script setup lang="ts">
    const config = useInitialData().getConfig;
    const menuServices = ref([
        {
            label: 'Términos y Condiciones',
            key: 'terminos-condiciones',
            route: '/terminos-y-condiciones'
        },
        {
            label: 'Plazos de Entrega',
            key: 'plazos-entrega',
            route: '/plazos-de-entrega'
        },
        {
            label: 'Devoluciones',
            key: 'devoluciones',
            route: '/devoluciones'
        },
        {
            label: 'Programa de Distribuidores',
            key: 'programa-distribuidores',
            route: '/programa-de-distribuidores'
        }
    ]);
    const menuWhoWeAre = ref([
        {
            label: 'Quienes Somos',
            key: 'quienes-somos',
            route: '/quienes-somos'
        },
        {
            label: 'Política de Privacidad',
            key: 'politica-privacidad',
            route: '/politica-de-privacidad'
        },
        {
            label: 'Política de Cookies',
            key: 'politica-cookies',
            route: '/politica-de-cookies'
        }
    ]);

    const qrText = computed(() => {
        return useTools().formatPhoneNumber(config.phoneContact);
    });
</script>

<template>
    <div class="w-full h-fit bg-slate-950 text-slate-200 pt-20 pb-8">
        <div class="container mx-auto flex flex-col gap-8">
            <div class="border-b flex flex-col md:flex-row px-4 pb-20 md:px-0" style="border-color: rgba(255, 255, 255, 0.16);">
                <div class="w-full md:w-1/3 flex flex-col gap-6 mb-10 md:mb-0">
                    <NuxtLink to="/" class="mb-6">
                        <NuxtImg src="/images/logo_white.webp" class="h-16" alt="Logo EBP Publicidad" />
                    </NuxtLink>
                    <h3 class="text-xl font-medium">Contáctanos</h3>
                    <div class="flex flex-col gap-2 font-extralight">
                        <span>teléfono: {{ config.phoneContact }}</span>
                        <span>{{ config.emailContact }}</span>
                        <div class="w-fit h-fit bg-slate-50 rounded-md flex justify-center items-center my-2">
                            <ClientOnly>
                                <ArpixQrModal :value="qrText || ''" type="whatsapp" />
                            </ClientOnly>
                        </div>
                        <SocialNet />
                    </div>
                </div>
                <div class="w-full md:w-1/3 mb-10 md:mb-0 flex flex-col gap-6 mr-4">
                    <h3 class="text-xl font-medium">Servicio al Cliente</h3>
                    <div class="w-full h-fit">
                        <ul class="flex flex-col gap-2">
                            <li v-for="item in menuServices" :key="item.key" class="p-2 rounded-md hover:bg-[#ffffff11] cursor-pointer">
                                <NuxtLink :to="item.route">
                                    <span class="text-gray-400 px-1">{{ item.label }}</span>
                                </NuxtLink>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="w-full md:w-1/3 flex flex-col gap-6 mr-4">
                    <h3 class="text-xl font-medium">Acerca de Nosotros</h3>
                    <div class="w-full h-fit">
                        <ul class="flex flex-col gap-2">
                            <li v-for="item in menuWhoWeAre" :key="item.key" class="p-2 rounded-md hover:bg-[#ffffff11] cursor-pointer">
                                <NuxtLink :to="item.route">
                                    <span class="text-gray-400 px-1">{{ item.label }}</span>
                                </NuxtLink>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="w-full h-full px-4 md:px-0 pb-8 md:pb-0 flex flex-col md:flex-row justify-center items-center gap-2">
                <div class="w-fit text-xs">{{ new Date().getFullYear() }} © EBP Publicidad - Todos los derechos reservados</div> <span class="hidden md:block">|</span> 
                <ArpixLogo />
            </div>
        </div>
    </div>
</template>
