<script setup lang="ts">
    const props = defineProps({
        category: {
            type: String,
            required: true
        }
    });

    const responsiveOptions = ref([
        {
            breakpoint: '1400px',
            numVisible: 5,
            numScroll: 1
        },
        {
            breakpoint: '1199px',
            numVisible: 4,
            numScroll: 1
        },
        {
            breakpoint: '767px',
            numVisible: 2,
            numScroll: 1
        },
        {
            breakpoint: '575px',
            numVisible: 1,
            numScroll: 1
        }
    ]);
    const loading = ref(true);
    const filterQuery = {
        filter: {
            categories: {
                category: {
                    slug: {
                        _in: [props.category]
                    }
                }
            },
            variants: {stock: {_gt: 0}, price_1: {_gt: "0"}}
        },
        limit: 15,
        offset: 0,
        order_by: { id: 'asc' }
    }

    const { products, total } = await useEnyesShop().getProducts(filterQuery).finally(() => loading.value = false);
</script>

<template>
    <div v-if="!loading && products && total > 0" class="w-full h-full flex flex-col gap-6 py-20 bg-slate-100">
        <div class="w-full h-fit flex flex-col items-center">
            <h2 class="text-center text-3xl md:text-4xl josefin-title">Productos de Escritura</h2>
        </div>
        <div v-if="!products || total === 0" class="w-full h-60 flex justify-center items-center">
            <AxEmpty message="No se encontraron Productos." />
        </div>

        <div v-else class="w-full h-full">
            <ClientOnly >
                <div id="product-carousel" class="px-4">
                    <Carousel :value="products" :numVisible="4" :numScroll="1" :responsiveOptions="responsiveOptions" circular :autoplayInterval="3000">
                        <template #item="slotProps">
                            <NuxtLink :to="`/producto/${ slotProps.data?.slug }`" target="_blank">
                                <div class="!h-full">
                                    <div class="!h-[96%] border border-surface-200 dark:border-surface-700 m-2 p-3 hover:shadow-md bg-white flex flex-col justify-between">
                                        <div class="mb-4 w-full h-full">
                                            <div class="relative mx-auto w-full h-full flex justify-center items-center">
                                                <img :src="slotProps.data?.image" :alt="slotProps.data.name" class="w-full max-w-[240px]" />
                                            </div>
                                        </div>
                                        <div class="w-full flex flex-col">
                                            <div v-tooltip.top="`${slotProps.data?.id} - ${slotProps.data?.name}`">
                                                <div class="mb-1 text-sm text-slate-900">
                                                    <div class="w-fit font-semibold text-md uppercase">
                                                        <div class="line-clamp-1 font-light">{{ slotProps.data?.id }}</div>
                                                        <div class="line-clamp-1">{{ slotProps.data?.name }}</div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="text-xs text-gray-400 line-clamp-2">{{ slotProps.data?.description || 'Sin Descripción' }}</div>

                                            <div class="flex justify-between items-center mt-2">
                                                <div class="mt-0 font-semibold text-lg">
                                                    <span class="text-xs font-light">Desde</span> 
                                                    {{ slotProps.data.variants[0].price_1 }} €
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </NuxtLink>
                        </template>
                    </Carousel>
                </div>
            </ClientOnly>
        </div>
    </div>
</template>
