<script setup lang="ts">
    const categories = [
        { name: "Escritura", image: "icon-park-twotone:writing-fluently", slug: "articulos-de-escritura-10101" },
        { name: "Encendedores", image: "game-icons:lighter", slug: "encendedores-co-10104" },
        { name: "<PERSON><PERSON><PERSON>", image: "game-icons:car-key", slug: "llaveros-linternas-10103" },
        { name: "<PERSON><PERSON><PERSON>", image: "famicons:gift" , slug: "regalos-10107" },
        { name: "Tecnología", image: "ic:outline-power-settings-new", slug: "tecnologia-10105" },
        { name: "<PERSON><PERSON><PERSON><PERSON>", image: "ic:baseline-business-center", slug: "negocios-oficina-10106" },
        { name: "LifeStyle", image: "material-symbols:shopping-bag-speed-rounded", slug: "lifestyle-10111" },
    ];
</script>

<template>
    <div class="w-full h-full bg-slate-50">
        <section class="w-full h-full container mx-auto px-2 py-20 flex flex-col justify-center items-center">
            <h2 class="text-3xl md:text-4xl josefin-title mb-1">Categorías Destacadas</h2>

            <div class="w-full px-4 py-6 overflow-x-auto md:overflow-visible scrollbar-hide flex justify-start md:justify-center">
                <div class="w-fit flex flex-row gap-10">
                    <div v-for="(category, index) in categories" :key="index" class="flex flex-col items-center">
                        <NuxtLink :to="`/catalogo/${ category.slug }`">
                            <div class="flex items-center justify-center bg-white shadow hover:shadow-md !w-[124px] !h-[124px] md:min-w-0">
                                <!-- <NuxtImg :src="category.image" class="!w-28 !h-28 object-cover object-top" :alt="category.name" quality="60" format="webp" /> -->
                                <Icon :name="category.image" size="56" class="text-primary-900" />
                            </div>
                        </NuxtLink>
                        <span class="mt-2 font-semibold text-gray-800">{{ category.name }}</span>
                    </div>
                </div>
            </div>
        </section>
    </div>
</template>

<style lang="css" scoped>
    .scrollbar-hide {
        -ms-overflow-style: none;  /* IE y Edge */
        scrollbar-width: none;  /* Firefox */
    }
    .scrollbar-hide::-webkit-scrollbar {
        display: none; /* Chrome, Safari y Opera */
    }
</style>
