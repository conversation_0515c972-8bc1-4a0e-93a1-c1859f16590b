<template>
  <div class="products-queue-manager p-6 bg-white rounded-lg shadow-lg">
    <h2 class="text-2xl font-bold mb-6">Gestión de Procesamiento de Productos</h2>
    
    <!-- Botones de acción -->
    <div class="flex gap-4 mb-6">
      <button
        @click="processWithQueue"
        :disabled="isProcessing"
        class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <span v-if="isProcessing" class="flex items-center">
          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Procesando...
        </span>
        <span v-else>Procesar con Cola</span>
      </button>
      
      <button
        @click="processDirectly"
        :disabled="isProcessing"
        class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Procesar Directamente
      </button>
      
      <button
        @click="refreshQueueStatus"
        class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
      >
        Actualizar Estado
      </button>
    </div>

    <!-- Trabajo actual -->
    <div v-if="currentJob" class="mb-6 p-4 border rounded-lg">
      <h3 class="text-lg font-semibold mb-2">Trabajo Actual</h3>
      <div class="grid grid-cols-2 gap-4">
        <div>
          <span class="font-medium">ID:</span> {{ currentJob.jobId }}
        </div>
        <div>
          <span class="font-medium">Estado:</span>
          <span :class="getStateColor(currentJob.state)" class="ml-2 font-semibold">
            {{ formatState(currentJob.state) }}
          </span>
        </div>
        <div>
          <span class="font-medium">Progreso:</span> {{ formatProgress(currentJob.progress) }}
        </div>
        <div>
          <span class="font-medium">Creado:</span> {{ formatDate(currentJob.createdAt) }}
        </div>
      </div>
      
      <!-- Barra de progreso -->
      <div v-if="typeof currentJob.progress === 'number'" class="mt-4">
        <div class="w-full bg-gray-200 rounded-full h-2.5">
          <div 
            class="bg-blue-600 h-2.5 rounded-full transition-all duration-300"
            :style="{ width: `${currentJob.progress}%` }"
          ></div>
        </div>
      </div>

      <!-- Error si existe -->
      <div v-if="currentJob.error" class="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
        <strong>Error:</strong> {{ currentJob.error }}
      </div>

      <!-- Resultado si está completado -->
      <div v-if="currentJob.state === 'completed' && currentJob.result" class="mt-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
        <strong>Completado:</strong>
        <ul class="mt-2 text-sm">
          <li>Productos: {{ currentJob.result.product?.length || 0 }}</li>
          <li>Colores: {{ currentJob.result.colors?.length || 0 }}</li>
          <li>Tallas: {{ currentJob.result.sizes?.length || 0 }}</li>
          <li>Variantes: {{ currentJob.result.variant?.length || 0 }}</li>
        </ul>
      </div>
    </div>

    <!-- Estado de la cola -->
    <div v-if="queueStatus" class="mb-6">
      <h3 class="text-lg font-semibold mb-4">Estado de la Cola</h3>
      
      <!-- Contadores -->
      <div class="grid grid-cols-4 gap-4 mb-4">
        <div class="text-center p-3 bg-yellow-100 rounded">
          <div class="text-2xl font-bold text-yellow-600">{{ queueStatus.counts.waiting }}</div>
          <div class="text-sm text-yellow-800">En Espera</div>
        </div>
        <div class="text-center p-3 bg-blue-100 rounded">
          <div class="text-2xl font-bold text-blue-600">{{ queueStatus.counts.active }}</div>
          <div class="text-sm text-blue-800">Activos</div>
        </div>
        <div class="text-center p-3 bg-green-100 rounded">
          <div class="text-2xl font-bold text-green-600">{{ queueStatus.counts.completed }}</div>
          <div class="text-sm text-green-800">Completados</div>
        </div>
        <div class="text-center p-3 bg-red-100 rounded">
          <div class="text-2xl font-bold text-red-600">{{ queueStatus.counts.failed }}</div>
          <div class="text-sm text-red-800">Fallidos</div>
        </div>
      </div>

      <!-- Lista de trabajos recientes -->
      <div v-if="recentJobs.length > 0">
        <h4 class="font-medium mb-2">Trabajos Recientes</h4>
        <div class="space-y-2">
          <div 
            v-for="job in recentJobs" 
            :key="job.jobId"
            class="flex justify-between items-center p-2 bg-gray-50 rounded"
          >
            <div>
              <span class="font-mono text-sm">{{ job.jobId }}</span>
              <span :class="getStateColor(job.state)" class="ml-2 text-sm font-medium">
                {{ formatState(job.state) }}
              </span>
            </div>
            <div class="text-sm text-gray-600">
              {{ formatDate(job.createdAt) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Mensajes -->
    <div v-if="message" class="mt-4 p-3 rounded" :class="messageClass">
      {{ message }}
    </div>
  </div>
</template>

<script setup lang="ts">
const {
  isProcessing,
  currentJob,
  queueStatus,
  processProducts,
  getQueueStatus,
  formatProgress,
  formatState,
  getStateColor
} = useProductsQueue()

const message = ref('')
const messageClass = ref('')

// Trabajos recientes combinados
const recentJobs = computed(() => {
  if (!queueStatus.value) return []
  
  const allJobs = [
    ...queueStatus.value.jobs.active,
    ...queueStatus.value.jobs.completed,
    ...queueStatus.value.jobs.failed
  ]
  
  return allJobs
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 5)
})

const processWithQueue = async () => {
  try {
    const result = await processProducts(true)
    showMessage('Trabajo agregado a la cola exitosamente', 'success')
  } catch (error: any) {
    showMessage(`Error: ${error.message}`, 'error')
  }
}

const processDirectly = async () => {
  try {
    const result = await processProducts(false)
    showMessage('Productos procesados exitosamente', 'success')
  } catch (error: any) {
    showMessage(`Error: ${error.message}`, 'error')
  }
}

const refreshQueueStatus = async () => {
  try {
    await getQueueStatus()
    showMessage('Estado actualizado', 'success')
  } catch (error: any) {
    showMessage(`Error al actualizar: ${error.message}`, 'error')
  }
}

const showMessage = (text: string, type: 'success' | 'error') => {
  message.value = text
  messageClass.value = type === 'success' 
    ? 'bg-green-100 border border-green-400 text-green-700'
    : 'bg-red-100 border border-red-400 text-red-700'
  
  setTimeout(() => {
    message.value = ''
  }, 5000)
}

const formatDate = (date: Date | string) => {
  return new Date(date).toLocaleString()
}

// Cargar estado inicial
onMounted(() => {
  refreshQueueStatus()
})

// Auto-refresh cada 30 segundos
const autoRefresh = setInterval(() => {
  if (!isProcessing.value) {
    refreshQueueStatus()
  }
}, 30000)

onUnmounted(() => {
  clearInterval(autoRefresh)
})
</script>

<style scoped>
.products-queue-manager {
  max-width: 800px;
}
</style>
