<script setup lang="ts">

</script>

<template>
    <div class="flex flex-col items-start md:items-end pl-6 md:pl-0 gap-5">
        <div class="text-4xl font-extralight tracking-wide uppercase">Menú</div>
        <ul class="flex flex-col items-start md:items-end gap-2 text-lg font-extralight">
            <li><NuxtLink active-class="text-active font-medium" to="/profile">Datos del usuario</NuxtLink></li>
            <li><NuxtLink active-class="text-active font-medium" to="/profile/orders">Historial de pedidos</NuxtLink></li>
        </ul>
    </div>
</template>
