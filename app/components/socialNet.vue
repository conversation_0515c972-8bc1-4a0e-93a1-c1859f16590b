<script setup lang="ts">
    const config = useInitialData().getConfig;
</script>

<template>
    <div>
        <NuxtLink
            v-for="social in config.socialNet"
            :key="social.key"
            :to="social.link"
            target="_blank"
            class=""
        >
            <div class="w-9 h-9 p-1 flex justify-center items-center text-slate-200 border border-slate-700 rounded-md hover:cursor-pointer hover:shadow-md hover:border-slate-700 hover:bg-slate-700 hover:text-white">
                <Icon :name="social.icon" size="20" />
            </div>
        </NuxtLink>
    </div>
</template>
