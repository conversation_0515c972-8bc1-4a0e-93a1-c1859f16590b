<script setup lang="ts">
    const props = defineProps({
        mask: {
            type: String,
            required: true,
        },
        modelValue: {
            type: String,
            default: '',
        },
        placeholder: {
            type: String,
            default: '',
        },
        uppercase: {
            type: Boolean,
            default: false,
        },
    });

    const emit = defineEmits(['update:modelValue']);
    const { mask, modelValue, uppercase } = toRefs(props);
    const maskedValue = ref<any>(modelValue.value);

    watch(modelValue, (_val) => {
        maskedValue.value = _val;
    });

    const onInput = (event: any) => {
        const rawValue = event.target.value;
        const transformedValue = uppercase.value ? rawValue.toUpperCase() : rawValue;
        emit('update:modelValue', transformedValue);
    };
</script>

<template>
    <InputMask
        :mask="mask"
        v-model="maskedValue"
        @change="onInput"
    />
</template>
