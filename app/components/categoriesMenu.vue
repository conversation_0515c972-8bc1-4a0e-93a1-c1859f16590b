<script setup lang="ts">
    const props = defineProps({
        type: {
            type: String,
            default: 'text'
        },
        label: { type: String, default: 'CATEGORÍAS' }
    });

    const visible = ref(false);
    const xx = useEnyesShop()

    const { categories, loading, error, refresh } = await xx.getCategories();

    const categoriesFiltered = computed(() => {
        return categories.value?.filter((item: any) => item.in_menu);
    });

    const capitalize = (text: string) => {
        return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase()
    }
</script>

<template>
    <div>
        <Drawer v-model:visible="visible" position="full" class="pb-10">
            <template #header>
                <div class="w-full h-full flex justify-center items-center">
                    <SearchComponent @searching="() => visible = false" />
                </div>
            </template>
            <div class="w-full h-full container mx-auto mt-2">
                <div v-if="categories?.length" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mx-4">
                    <div v-for="item in categoriesFiltered" :key="item.id" class="w-full h-full flex flex-col gap-4 bg-slate-50 rounded-lg border border-slate-200 p-4">
                        <NuxtLink :to="`/catalogo/${ item.slug }`" @click="visible = false">
                            <span class="text-primary-700 hover:text-primary-800 text-lg uppercase font-semibold">{{ item.name }}</span>
                        </NuxtLink>

                        <div v-if="item.subcategories.length" class="w-full h-fit flex flex-col gap-4">
                            <div v-for="subitem in item.subcategories" :key="subitem.id" class="w-full h-fit flex flex-col gap-2 group">
                                <NuxtLink :to="`/catalogo?subcategories=${ subitem.slug }`" class="indent-4 group-hover:text-red-600 text-slate-600 hover:text-red-600 text-[15px] !font-light capitalize" @click="visible = false">
                                    <span>{{ capitalize(subitem.name) }}</span>
                                </NuxtLink>

                                <!-- <div v-if="subitem.subcategories.length" class="w-full h-fit flex flex-col gap-2">
                                    <div v-for="subsubitem in subitem.subcategories" :key="subsubitem.id" class="w-full h-fit flex flex-col">
                                        <NuxtLink :to="`/catalogo?subcategories=${ subsubitem.slug }`" class="indent-8" @click="visible = false">
                                            <span class="text-slate-600 hover:text-slate-900 text-[15px] font-light">{{ subsubitem.name }}</span>
                                        </NuxtLink>
                                    </div>
                                </div> -->
                            </div>
                        </div>
                    </div>
                </div>

                <div v-else-if="loading" v-for="i in 4" :key="i" class="w-full h-full flex flex-col mb-4 bg-slate-50 rounded-xl border border-slate-200 px-4 py-2">
                    <Skeleton class="w-full h-full" />
                </div>

                <div v-else class="w-full h-full flex justify-center items-center">
                    <AxEmpty message="Categorías no encontradas" />
                </div>
            </div>
        </Drawer>

        <div class="w-fit cursor-pointer flex justify-center items-center" @click="visible = true">
            <span v-if="props.type === 'text'">{{ props.label }}</span>
            <Icon v-else-if="props.type === 'icon'" name="akar-icons:search" size="26" />
        </div>
    </div>
</template>
