<script setup lang="ts">
    const visible = ref<boolean>(false);
    const type = ref<string>('bank');
    const reference = ref<string | null>(null);

    const emit = defineEmits(['data']);

    const onSended = () => {
        if (type.value === 'bank' || type.value === 'bizum') {
            emit('data', {
                type: type.value,
                reference: useTools().getRandomString(20)
            });
            reference.value = null;
            visible.value = false;
        }
    }
</script>

<template>
    <div class="w-full">
        <div class="mb-3">
            <p class="font-light">
                Transferencia a nuestra Cuenta bancaria:
                <b>SABADELL: ES0500812800800009019817</b><br>
                <b>Bizum al número de teléfono: 661 277 982</b>
            </p>
        </div>

        <Button class="w-full h-[45px] md:h-[35px] xl:h-[45px]" @click="visible = true">
            <span class="text-[15px]">Registrar pago</span>
        </Button>

        <Dialog v-model:visible="visible" modal header="Métodos de Pago Alternativos" :style="{ width: '25rem' }" :breakpoints="{ '1199px': '25rem', '575px': '90vw' }">
            <div class="w-full flex flex-col gap-3">
                <div class="flex gap-2">
                    <RadioButton v-model="type" inputId="bank" value="bank" />
                    <label for="bank" class="!w-full">SABADELL: ES0500812800800009019817</label>
                </div>
                <div class="flex gap-2">
                    <RadioButton v-model="type" inputId="bizum" value="bizum" />
                    <label for="bizum" class="!w-full">Bizum al número de teléfono: 661 277 982</label>
                </div>

                <!-- <InputText type="text" v-model="reference" placeholder="Nro. de referencia" /> -->
            </div>
            <div class="flex justify-end mt-6">
                <Button type="button" label="Enviar" @click="onSended"></Button>
            </div>
        </Dialog>
    </div>
</template>
