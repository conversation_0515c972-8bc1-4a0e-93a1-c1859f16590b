<script setup lang="ts">
    const props = defineProps({
        color: {
            type: Object,
            required: true
        }
    })
</script>

<template>
    <div class="w-8 h-8 border border-slate-300 rounded-full bg-cover bg-center flex justify-center items-center">
        <div v-if="color.id === 'surtidos'" class="w-full h-full rounded-full" style="background: conic-gradient(red, orange, yellow, green, blue, indigo, violet, red);"></div>
        <div v-else-if="color.hexcode && !color.hexcode.includes('-')" class="w-full h-full rounded-full" :style="{ backgroundColor: color.hexcode }"></div>
        <div v-else-if="color.hexcode && color.hexcode.includes('-')" class="w-full h-full rounded-full" :style="{ background: `linear-gradient(45deg, ${color.hexcode.split('-')[0]} 0% 49%, ${color.hexcode.split('-')[0]} 48%, ${color.hexcode.split('-')[1]} 52%, ${color.hexcode.split('-')[1]} 100%)` }"></div>
        <div v-else-if="color.url" class="w-full h-full rounded-full" :style="{ backgroundImage: `url(${color.url})` }"></div>
    </div>
</template>
