<script setup lang="ts">
    import type { FormField } from './FormTypes';
    import AxField from "./AxField.vue";

    const props = defineProps<{
        fields: FormField[]
        labelType: string
        labelVariant?: string
        labelCss?: string
        modelValue: Record<string, any>
        errors: Record<string, string>
        currentStep: number
        loading: boolean;
        submitLabel?: string
        loadingLabel?: string
        removeFile?: string
        showProgressBar?: boolean
    }>();

    const { loading } = toRefs(props);

    const emit = defineEmits<{
        'change': [any]
        'update:currentStep': [step: number]
        'filesUploaded': [any]
        next: []
        prev: []
        submit: []
    }>();

    const steps = computed(() => 
        props.fields.map((field, index) => ({
            index,
            label: field.label,
            command: () => emit('update:currentStep', index)
        }))
    );

    const isLastStep = computed(() => 
        props.currentStep === props.fields.length - 1
    );

    const modifiedFields = props.fields.map((field) => {
        return { ...field, span: 1 };
    });

    const nextStep = () => {
        if (isLastStep.value) {
            emit('submit')
        } else {
            emit('next')
        }
    }

    const prevStep = () => {
        emit('prev')
    }
</script>

<template>
    <div class="form-stepper">
        <ProgressBar v-if="showProgressBar" :value="(currentStep) / steps.length * 100 " :showValue="false" style="height: 6px" class="bg-salte-200"></ProgressBar>

        <Stepper :value="currentStep" linear>
            <StepList>
                <div class="w-full px-2">
                    <Step v-for="(step, index) in steps" v-slot="{  }" asChild :value="index">
                        <div v-if="index === currentStep" class="w-full flex gap-4">
                            <Divider />
                            <div class="w-[520px] h-[100px] flex justify-center items-center gap-3 bg-transparent border-0">
                                <div class="font-bold text-3xl md:text-4xl text-slate-500">{{ index + 1 }} <span class="text-xl md:text-3xl">de</span> {{ steps.length }}</div>
                            </div>
                            <Divider />
                        </div>
                    </Step>
                </div>
            </StepList>

            <StepPanels>
                <StepPanel v-for="(field, index) in modifiedFields" :key="field.name" :value="index">
                    <div class="transition-all duration-300 p-0 md:p-2 mt-4">
                        <AxField
                            :field="field"
                            :label-type="labelType ?? 'standard'"
                            :label-variant="labelVariant"
                            :label-css="labelCss"
                            v-model="modelValue[field.name]"
                            :error="errors[field.name]"
                            @update:model-value="(event) => emit('change', { code: field.name, data: event })"
                            @files-uploaded="(u: any) => emit('filesUploaded', { [field.name]: u })"
                        />
                    </div>

                    <div class="flex pt-12 md:pt-6 justify-between">
                        <Button
                            type="button"
                            @click="prevStep"
                            :disabled="currentStep === 0 || loading"
                            severity="secondary"
                        >
                            <div class="w-full flex items-center gap-1">
                                <Icon name="akar-icons:arrow-left" />
                                <span>Anterior</span>
                            </div>
                        </Button>

                        <Button
                            type="button"
                            @click="nextStep"
                            :loading="loading"
                        >
                            <template #default>
                                <div class="w-full flex justify-center items-center gap-2">
                                    <span v-if="loading" class="flex items-center gap-1">
                                        <Icon name="akar-icons:arrow-cycle" class="animate-spin" />
                                        {{ loadingLabel }}
                                    </span>
                                    <span v-else class="flex items-center gap-1">
                                        {{ isLastStep ? submitLabel : 'Siguiente' }}
                                        <Icon v-if="!isLastStep" name="akar-icons:arrow-right" />
                                    </span>
                                </div>
                            </template>
                        </Button>
                    </div>
                </StepPanel>
            </StepPanels>
        </Stepper>
    </div>
</template>

<style lang="css" scoped>
    .p-steppanels {
        @apply !p-0
    }

    :deep(.p-progressbar) {
        height: 6px;
        position: absolute;
        top: 0px;
        z-index: 10;
        width: 99.6%;
        left: 2px;
        border-top-right-radius: 10px;
        border-bottom-right-radius: 0;
        border-top-left-radius: 10px;
        border-bottom-left-radius: 0;
    }
</style>
