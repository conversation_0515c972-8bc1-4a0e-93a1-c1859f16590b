<script setup lang="ts">
    defineProps({
        message: {
            type: String,
            default: ''
        },
        type: {
            type: String,
            default: 'error',
            validator: (value: string) => ['error', 'success'].includes(value)
        }
    })
</script>

<template>
    <Message v-if="message" :severity="type" class="mb-4"><p class="!text-sm !font-normal">{{ message }}</p></Message>
</template>
