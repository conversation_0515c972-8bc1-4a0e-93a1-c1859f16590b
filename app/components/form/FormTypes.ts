import { z } from 'zod'

export interface FormField {
    name: string
    label?: string
    labelAddon?: string
    component: string
    props: Record<string, any>
    validation?: z.ZodType<any>
    defaultValue?: any
    description?: string
    required?: boolean
    span?: number
    checkboxGroup?: boolean
    options?: any
    messageFeatured?: string
}

export interface FormConfig {
    title?: string
    stepByStep: boolean
    fields: FormField[]
    submitLabel?: string
    loadingLabel?: string
    description?: string
    labelType?: 'Standard' | 'FloatLabel' | 'IftaLabel' | 'none'
    labelVariant?: 'in' | 'on'
    labelCss?: string
    showProgressBar?: boolean
}

export interface FormErrors {
    [key: string]: string
}

export interface FormData {
    [key: string]: any
}