<script setup lang="ts">
    import type { FormConfig, FormData, FormErrors } from './FormTypes';
    import { z } from 'zod';
    import AxField from "./AxField.vue";

    const props = defineProps<{
        formConfig: FormConfig;
        loading: boolean;
        removeFile?: string;
    }>();

    const emit = defineEmits<{
        submit: [data: FormData];
        error: [errors: FormErrors];
        'update:loading': [boolean];
        'update:modelValue': [any];
    }>();

    const { loading, formConfig } = toRefs(props);
    const formData = ref<FormData>({});
    const errors = ref<FormErrors>({});
    const currentStep = ref(0);
    const formMessage = ref('');
    const formMessageType = ref<'error' | 'success'>('error');
    const uploaded = ref<any>({});

    const transformFormConfig = computed(() => {
        const transformedFields = [];

        for (const field of formConfig.value.fields) {
            if (Array.isArray(field.name)) {
                field.name.forEach((name, index) => {
                    transformedFields.push({
                        ...field,
                        name,
                        component: Array.isArray(field.component) ? field.component[index] : field.component,
                        props: Array.isArray(field.props) ? field.props[index] : field.props,
                        validation: Array.isArray(field.validation) ? field.validation[index] : field.validation,
                        defaultValue: Array.isArray(field.defaultValue) ? field.defaultValue[index] : field.defaultValue,
                    });
                });
            } else {
                transformedFields.push(field);
            }
        }

        return {
            ...formConfig,
            fields: transformedFields,
        };
    });
    
    const validateField = async (fieldName: string) => {
        const field: any = transformFormConfig.value?.fields?.find(f => f.name === fieldName);
        if (!field?.validation) return true;

        try {
            if (Array.isArray(formData.value[fieldName])) {
                const allData: any[] = [];
                formData.value[fieldName].forEach(async data => {
                    allData.push(data);
                })

                await field?.validation[0].parseAsync(allData[0]);
                await field?.validation[1].parseAsync(allData[1]);
            } else {
                await field.validation.parseAsync(formData.value[fieldName]);
            }

            errors.value[fieldName] = '';
            return true;
        } catch (err: any) {
            if (err instanceof z.ZodError) {
                errors.value[fieldName] = err.errors[0]?.message || 'Error de validación';
            }
            return false;
        }
    };

    const validateForm = async () => {
        const validations = await Promise.all(
            formConfig.value.fields.map(async (field) => {
                if (Array.isArray(field.name)) {
                    let resp = false;
                    field.name.forEach(async (name) => {
                        resp = await validateField(name);
                    });
                    return resp;
                }

                if (field.component === 'FileUpload') return validateFiles(field);
                else return await validateField(field.name);
            })
        );
        return validations.every(v => v);
    }

    const validateFiles = (field: any) => {
        if (field?.required && field.component === 'FileUpload' && (!uploaded.value[field.name] || uploaded.value[field.name]?.length === 0)) {
            errors.value = { [field.name]: field?.validation?.message || 'Error' }
            return false;
        }

        if (field?.required && field.component === 'FileUpload' && (uploaded.value[field.name]?.length > field.props.fileLimit)) {
            errors.value = { [field.name]: `Solo es permitido un máximo de ${ field.props.fileLimit } archivos` }
            return false;
        }

        delete errors.value[field.name];
        return true;
    }

    const handleNext = async () => {
        const field = formConfig.value?.fields?.[currentStep.value];
        if (!field) return;

        let isValid = false;
        if (field.component === 'FileUpload') isValid = validateFiles(field);
        else isValid = await validateField(field.name);

        if (isValid) currentStep.value++;
    };

    const handlePrev = () => {
        if (currentStep.value > 0) {
            currentStep.value--;
        }
    }

    const handleSubmit = async () => {
        formMessage.value = '';

        const isValid = await validateForm();
        if (!isValid) {
            emit('error', errors.value);
            formMessage.value = 'Por favor, corrija los errores del formulario.';
            formMessageType.value = 'error';
            return;
        }

        Object.keys(uploaded.value).forEach((index: string) => {
            formData.value[index] = uploaded.value[index];
        });
        
        emit('submit', formData.value);
    }

    watch(() => formConfig.value?.fields, (fields) => {
        if (fields) {
            fields.forEach(field => {
                if (!(field.name in formData.value)) {
                    formData.value[field.name] = field.defaultValue ?? null;
                }
            });
        }
    }, { immediate: true });
</script>

<template>
    <div class="w-full mx-auto p-2 md:p-0" :class="(formConfig.stepByStep) ? 'max-w-3xl' : 'max-w-4xl'">
        <Card class="border border-gray-200 shadow-xl relative">
            <template v-if="formConfig?.title" #title>
                <div class="flex flex-col gap-1 mb-6">
                    <h2 class="text-2xl font-bold">{{ formConfig.title }}</h2>
                    <p v-if="formConfig.description" class="text-sm text-gray-500">
                        {{ formConfig.description }}
                    </p>
                </div>
            </template>

            <template #content>
                <slot name="alternativeTitle"></slot>

                <!-- <FormMessage
                    :message="formMessage"
                    :type="formMessageType"
                /> -->

                <form @submit.prevent="handleSubmit" class="space-y-5 w-full">
                    <template v-if="formConfig.stepByStep">
                        <FormStepper
                            :fields="formConfig.fields"
                            :label-type="formConfig?.labelType ?? 'standard'"
                            :label-variant="formConfig.labelVariant"
                            :label-css="formConfig.labelCss"
                            v-model="formData"
                            :errors="errors"
                            :current-step="currentStep"
                            :show-progress-bar="formConfig?.showProgressBar"
                            @next="handleNext"
                            @prev="handlePrev"
                            @submit="handleSubmit"
                            :loading="loading"
                            :submit-label="formConfig?.submitLabel || 'Enviar'"
                            :loading-label="formConfig?.loadingLabel || 'Enviando...'"
                            @change="(v: any) => emit('update:modelValue', { code: v.code, data: v.data })"
                            @files-uploaded="(u: any) => uploaded = u"
                        />
                    </template>

                    <template v-else>
                        <div class="flex flex-wrap gap-4">
                            <AxField
                                v-for="field in formConfig.fields ?? []"
                                :key="field.name"
                                :field="field"
                                :label-type="formConfig?.labelType ?? 'standard'"
                                :label-variant="formConfig.labelVariant"
                                v-model="formData[field.name]"
                                :error="errors[field.name]"
                                @update:model-value="(event) => emit('update:modelValue', { code: field.name, data: event })"
                                @files-uploaded="(u: any) => uploaded[field.name] = u"
                            />
                        </div>

                        <slot name="bottom"></slot>

                        <Button
                            type="submit"
                            class="w-full"
                            :loading="loading"
                        >
                            <template #default>
                                <div class="w-full flex justify-center items-center gap-2">
                                    <span v-if="loading" class="flex items-center gap-1">
                                        <Icon name="akar-icons:arrow-cycle" class="animate-spin" />
                                        {{ formConfig?.loadingLabel || 'Enviando...' }}
                                    </span>
                                    <span v-else class="flex items-center gap-1 font-semibold">
                                        {{ formConfig?.submitLabel || 'Enviar' }}
                                    </span>
                                </div>
                            </template>
                        </Button>
                    </template>
                </form>
            </template>
        </Card>
    </div>
</template>

<style lang="css" scoped>
    .p-card-body {
        @apply !p-2
    }
</style>
