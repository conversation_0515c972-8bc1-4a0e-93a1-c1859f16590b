<script setup lang="ts">
    import type { FormField } from './FormTypes';
    import AxFileUpload from '~/components/form/AxFileUpload.vue';
    import { 
        InputText, 
        TreeSelect,
        InputNumber,
        Textarea,
        Select,
        MultiSelect,
        Checkbox,
        RadioButton,
        ColorPicker,
        DatePicker,
        InputMask,
        InputOtp,
        Password,
        Rating,
        SelectButton,
        Slider,
        ToggleButton,
        ToggleSwitch,
        FloatLabel,
        IftaLabel,
        FileUpload
    } from "primevue";
    import AxInputMask from '~/components/axInputMask.vue';
    import { h } from "vue";

    const props = withDefaults(
        defineProps<{
            field: FormField
            labelType: string
            labelVariant?: string
            labelCss?: string
            modelValue: any
            error?: string
        }>(),
        {
            labelType: 'Standard'
        }
    );

    const { field, labelType } = toRefs(props);
    const localValue1 = ref();
    const localValue2 = ref();
    const filesArray = ref<any[]>([]);
    const $t = import.meta.client ? useNuxtApp().$t : (key: string) => key;


    if (Array.isArray(props.modelValue)) {
        localValue1.value = props.modelValue[0];
        localValue2.value = props.modelValue[1];
    }

    if (labelType.value === 'FloatLabel') {
        if (field.value.props && 'placeholder' in field.value.props) {
            delete field.value.props.placeholder;
        }
    }

    const emit = defineEmits<{
        'update:modelValue': [value: any]
        'deleteFile': [any]
        'filesUploaded': [any]
    }>();

    const localValue = computed({
        get: () => props.modelValue,
        set: (value) => {
            emit('update:modelValue', value);
        }
    });

    const componentsMap: Record<string, any> = {
        InputText,
        InputNumber,
        Textarea,
        Select,
        MultiSelect,
        Checkbox,
        RadioButton,
        ColorPicker,
        DatePicker,
        InputMask,
        InputOtp,
        Password,
        Rating,
        SelectButton,
        Slider,
        ToggleButton,
        ToggleSwitch,
        TreeSelect,
        AxInputMask,
        FloatLabel,
        IftaLabel,
        FileUpload,
        div: (props: any, { slots }: any) => h('div', props, slots.default?.())
    };

    const getStyle = (span: number) => {
        if (!span) return { width: '100% !important' };
        else if (span === 1) return { width: '100% !important' };
        else if (span === 2) return { width: `calc(50% - 8px) !important` };
        else if (span === 3) return { width: `calc(33.33% - 5.33px) !important` };
        else if (span === 4) return { width: `calc(25% - 6px) !important` };
    }

    const resolveComponent = (type: any) => {
        return componentsMap[type] || null;
    }

    const isInputComponent = (comp: any) => {
        return [InputText, InputNumber, InputMask, AxInputMask].includes(resolveComponent(comp));
    }

    watch(localValue1, (val) => {
        localValue.value = [localValue1.value, localValue2.value];
    });

    watch(localValue2, (val) => {
        localValue.value = [localValue1.value, localValue2.value];
    });

    watch(filesArray, (val) => {
        emit('filesUploaded', val);
    })

    const remainingChars = computed(() => field.value.props?.maxlength - (localValue.value?.length || 0));
</script>

<template>
    <div :style="getStyle(field?.span || 1)" :class="{ 'mb-5': labelType === 'FloatLabel' }">
        <div v-if="Array.isArray(field.component)" class="w-full">
            <label v-if="labelType === 'Standard' && field?.label" :for="field.name" class="block text-sm font-medium text-gray-700 mb-1" :class="labelCss">
                {{ field.label }}
                <span v-if="field.required" class="text-red-500">*</span>
            </label>
            <InputGroup>
                <template v-for="(comp, index) in field.component" :key="index">
                    <template v-if="isInputComponent(comp)">
                        <template v-if="labelType === 'Standard'">
                            <component
                                :is="resolveComponent(comp)"
                                :id="field.name"
                                v-bind="field.props[index] || {}"
                                v-model="localValue1"
                                :class="[ error ? 'p-invalid' : '', field.props[index]?.class ]"
                            />
                        </template>
                        <template v-else>
                            <component
                                :is="resolveComponent(labelType)"
                                :variant="labelVariant"
                                class="w-full"
                            >
                                <component
                                    :is="resolveComponent(comp)"
                                    :id="field.name"
                                    v-bind="field.props[index] || {}"
                                    v-model="localValue1"
                                    :class="[ error ? 'p-invalid' : '', field.props[index]?.class ]"
                                />
                                <label v-if="labelType !== 'Standard' && field?.label" :for="field.name" class="block text-sm font-medium text-gray-700 mb-1" :class="labelCss">
                                    {{ field.label }}
                                    <span v-if="field.required" class="text-red-500">*</span>
                                </label>
                            </component>
                        </template>
                    </template>
                    <template v-else>
                        <InputGroupAddon class="!p-0 flex items-center gap-1">
                            <label v-if="field.labelAddon" :for="field.name" class="block text-xs font-medium text-gray-700 mb-1">
                                {{ field.labelAddon }}
                            </label>
                            <component
                                :is="resolveComponent(comp)"
                                v-bind="field.props[index] || {}"
                                v-model="localValue2"
                                :class="[ '!border-0 !shadow-none', error ? 'p-invalid' : '', field.props[index]?.class ]"
                            />
                        </InputGroupAddon>
                    </template>
                </template>
            </InputGroup>
        </div>

        <div
            v-else-if="
                field.component !== 'Checkbox' &&
                field.component !== 'RadioButton' &&
                field.component !== 'ColorPicker' &&
                field.component !== 'Slider' &&
                field.component !== 'FileUpload'
            " 
            class="w-full mb-1"
            :class="{ 'flex justify-center': field.component === 'InputOtp' }"
        >
            <template v-if="!labelType || labelType === 'Standard' || labelType === 'none'">
                <div :class="[
                    field?.messageFeatured ? 'border rounded-md border-slate-400 p-2' : '', 
                    field.component === 'InputOtp' ? 'flex flex-col items-center w-[330px]' : ''
                ]">
                    <Message v-if="field?.messageFeatured" class="mb-4 p-2" severity="warn" size="small">{{ field.messageFeatured }}</Message>
                    <label v-if="field?.label" :for="field.name" class="block text-sm font-medium text-gray-700 mb-1" :class="labelCss">
                        {{ field.label }}
                        <span v-if="field.required" class="text-red-500">*</span>
                    </label>
                    <component
                        :is="componentsMap[field.component]"
                        :id="field.name"
                        v-bind="field.props || {}"
                        v-model="localValue"
                        :class="[ 'w-full', error ? 'p-invalid' : '', field.props.class ]"
                    />
                </div>
            </template>

            <template v-else>
                <component
                    :is="resolveComponent(labelType)"
                    :variant="labelVariant"
                    class="w-full"
                >
                    <component
                        :is="componentsMap[field.component]"
                        :id="field.name"
                        v-bind="field.props || {}"
                        v-model="localValue"
                        :class="[ 'w-full', error ? 'p-invalid' : '', field.props.class ]"
                    />
                    <label v-if="field?.label" :for="field.name" class="block text-sm font-medium text-gray-700 mb-1" :class="labelCss">
                        {{ field.label }}
                        <span v-if="field.required" class="text-red-500">*</span>
                    </label>
                </component>
            </template>
        </div>

        <div 
            v-else-if="
                (field.component === 'Checkbox' && !field?.checkboxGroup) || 
                field.component === 'RadioButton' || 
                field.component === 'ColorPicker'
            " 
            class="w-full flex items-center gap-2"
        >
            <component
                :is="componentsMap[field.component]"
                v-bind="field.props || {}"
                v-model="localValue"
                :class="[ 'w-full', error ? 'p-invalid' : '', field.props.class ]"
                :inputId="field.name"
            />
            <label v-if="field?.label" :for="field.name" class="w-full text-base font-medium text-gray-700 cursor-pointer" :class="labelCss">
                {{ field.label }}
                <span v-if="field.required" class="text-red-500">*</span>
            </label>
        </div>

        <div v-else-if="(field.component === 'Checkbox' && field?.checkboxGroup)">
            <label v-if="field?.label" :for="field.name" class="block text-sm font-medium text-gray-700 mb-1" :class="labelCss">
                {{ field.label }}
                <span v-if="field.required" class="text-red-500">*</span>
            </label>
            <div class="w-full flex flex-wrap border rounded-md p-2">
                <div v-for="option in field.options" class="w-full md:w-1/3 flex items-center gap-2 p-1.5">
                    <Checkbox v-model="localValue" :inputId="option.code" :name="field.name" :value="option.code" />
                    <label :for="option.code"> {{ option.name }} </label>
                </div>
            </div>
        </div>

        <div
            v-else-if="field.component === 'Slider'"
        >
            <!-- Pendiente -->
        </div>

        <div v-if="field.component !== 'FileUpload'" class="mt-1">
            <div class="flex justify-between">
                <p v-if="field.description && !error" class="w-full text-xs text-gray-400 pl-2 flex items-center gap-1">
                    <Icon name="akar-icons:info-fill" size="14" />
                    {{ field.description }}
                </p>
                <p v-if="error" class="w-full text-xs text-red-600 pl-2">
                    {{ error }}
                </p>

                <span v-if="field.props?.maxlength && field.props?.maxlength > 0" class="w-full text-sm text-gray-500 flex justify-end pr-2">{{ remainingChars }}</span>
            </div>
        </div>

        <div v-if="field.component === 'FileUpload'" class="w-full border rounded-md px-4 py-6 flex flex-col !items-start">
            <label v-if="field?.label" :for="field.name" class="block text-sm font-medium text-gray-700 mb-1" :class="labelCss">
                {{ field.label }}
                <span v-if="field.required" class="text-red-500">*</span>
            </label>
            <AxFileUpload :filesArray="filesArray" @response="(resp: any) => filesArray = resp" :max-file-size="field?.props?.maxFileSize || 1000000" :file-limit="field?.props?.fileLimit || 10" />

            <div class="mt-1">
                <p v-if="field.description && !error" class="w-full text-xs text-gray-400 pl-2 flex items-center gap-1">
                    <Icon name="akar-icons:info-fill" size="14" />
                    {{ field.description }}
                </p>
                <p v-if="error" class="w-full text-xs text-red-600 pl-2">
                    {{ error }}
                </p>
            </div>
        </div>
    </div>
</template>

<style lang="css" scoped>
    :deep(.p-inputtext) {
        width: 100% !important;
    }

    :deep(.p-inputgroupaddon) {
        min-width: 140px !important;
        width: 140px !important;
    }

    :deep(.p-message-info) {
        color: #678cde;
    }
</style>
