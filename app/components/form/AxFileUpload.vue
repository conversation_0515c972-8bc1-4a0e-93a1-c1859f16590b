<script setup lang="ts">
    const props = defineProps({
        filesArray: {
            type: Object,
            required: true
        },
        maxFileSize: Number,
        mime: String,
        fileLimit: {
            type: Number,
            required: true
        }
    });

    const toast = useToast();
    const $t = import.meta.client ? useNuxtApp().$t : (key: string) => key;
    const loading = ref<boolean>(false);
    const config: any = useRuntimeConfig();
    const apiURL = config?.site?.url || '';
    const minioBucket = config?.public?.minio?.buckets?.principal || '';
    const fileComponent = ref();
    const { filesArray, fileLimit } = toRefs(props);
    const emit = defineEmits(['response', 'deleted']);

    const onSelect = async (event: any) => {
        if (!event.files || event.files.length === 0) {
            toast.add({ 
                severity: 'error', 
                summary: 'Ocurrió un error', 
                detail: 'No hay archivos seleccionados.',
                life: 5000 });
            return;
        }

        const selectedFiles = Array.isArray(event.files) ? event.files : Array.from(event.files);
        const currentFiles = Array.isArray(filesArray.value) ? filesArray.value : [];

        if ((selectedFiles.length + currentFiles.length) <= fileLimit?.value) {
            loading.value = true;

            const formData = new FormData();

            selectedFiles.forEach((file: any) => {
                formData.append('files', file);
            });

            try {
                const response:any = await $fetch(`${ apiURL }/api/file/upload`, {
                    method: 'POST',
                    body: formData,
                    params: { bucket: minioBucket }
                });

                const files = response.map((item: any) => item.file);
                emit('response', [ ...currentFiles, ...files ]);
                loading.value = false;
            } catch (error) {
                console.error('Error al subir los archivos:', error);
            }
        } else {
            toast.add({ 
                severity: 'warn', 
                summary: `El número máximo de archivos que se pueden subir es ${ fileLimit.value }`,
                life: 5000 });
            return;
        }
    }

    const deleteFile = async (fileName: string, bucket: string) => {
        loading.value = true;
        try {
            await $fetch(`${ apiURL }/api/file/delete`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: {
                    fileName,
                    bucket
                }
            });

            const files = filesArray.value.filter((file: any) => file.fileName !== fileName);
            emit('response', files);
        } catch (err: any) {
            toast.add({ 
                severity: 'error', 
                summary: 'Ocurrió un error', 
                detail: $t(err.data?.message) || 
                    $t(err.message) ||
                    $t(err.response.errors[0].message) ||
                    $t('error.unknownError') || 'error.unknownError',
                life: 5000 });
        } finally {
            loading.value = false;
        }
    }
</script>

<template>
    <div class="w-full">
        <BlockUI :blocked="loading" class="w-full p-4">
            <FileUpload
                v-if="filesArray.length < fileLimit"
                ref="fileComponent"
                mode="basic"
                name="files"
                :accept="mime || 'image/*'" 
                :maxFileSize="maxFileSize || 1000000"
                :auto="true"
                :customUpload="true"
                chooseLabel="Selecciona el archivo"
                :multiple="true"
                :showUploadButton="false"
                :showCancelButton="false"
                invalidFileSizeMessage="El tamaño del archivo no es válido. Debe ser menor a {1}"
                invalidFileTypeMessage="{0}: Tipo de archivo no válido"
                invalidFileLimitMessage="El número máximo de archivos que se pueden subir es {0}"
                @select="onSelect"
            />

            <div class="!w-full border border-dashed border-slate-400 rounded-md min-h-[120px] flex flex-wrap gap-2 items-center p-2 mt-8">
                <div v-if="filesArray.length > 0" v-for="file in filesArray" class="border relative">
                    <Icon name="akar-icons:trash-can" class="w-4 h-4 absolute top-0 right-0 z-10 mr-1 mt-1 text-red-600 cursor-pointer" @click="deleteFile(file.fileName, file.bucket)" />
                    <NuxtImg :src="file.url" class="w-[133px] h-[100px]" />
                </div>
                <div v-else class="w-full flex justify-center items-center">
                    Sin archivos
                </div>
            </div>
        </BlockUI>
    </div>
</template>

<style lang="css" scoped>
    :deep(.p-fileupload.p-fileupload-basic.p-component) {
        @apply flex flex-col items-start
    }
</style>
