<script setup lang="ts">
    const route = useRoute();
    const router = useRouter();
    const searchText = ref<any>(route?.query?.search || '');

    const emit = defineEmits<{
        (e: 'searching', value: boolean): void;
    }>();

    const search = () => {
        const query = { ...route.query };

        if (searchText.value) {
            query.search = searchText.value;
        } else {
            delete query.search;
        }

        router.replace({ path: '/catalogo', query }).then(() => {
            emit('searching', !!searchText.value);
        });
    }

    const intro = (event: any) => {
        if (event.key === 'Enter') {
            search();
        }
    }

    watch(searchText, (_val) => {
        if (!_val && route.query.search) {
            const query = { ...route.query };
            delete query.search;
            router.replace({ path: route.path, query });
        }
    });

    watch(() => route.query.search, (newSearch) => {
        searchText.value = newSearch || '';
    });
</script>

<template>
    <div class="w-full max-w-lg">
        <InputGroup>
            <InputText 
                autofocus
                placeholder="Buscar por nombre o referencia" 
                v-model="searchText" 
                clearable 
                @keydown="intro" 
                fluid 
                size="small" 
            />
            <InputGroupAddon>
                <Button severity="primary" variant="text" @click="search" size="small" name="search">
                    <Icon name="ic:outline-search" size="25" class="text-slate-700" />
                </Button>
            </InputGroupAddon>
        </InputGroup>
    </div>
</template>
