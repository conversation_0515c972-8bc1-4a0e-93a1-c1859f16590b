export default defineNuxtPlugin(nuxtApp => {
    const config = useRuntimeConfig()

    const loadPayPalScript = async () => {
        if (import.meta.server) return null

        if (!config.public.paypal.clientId) {
            console.error('PayPal Client ID is missing')
            throw new Error('PayPal Client ID is missing')
        }

        const existingScript = document.querySelector('script[src*="paypal.com/sdk/js"]')
        if (existingScript) {
            return window.paypal ? window.paypal : new Promise((resolve, reject) => {
                existingScript.onload = () => resolve(window.paypal)
                existingScript.onerror = (error) => reject(error)
            })
        }

        return new Promise((resolve, reject) => {
            const script = document.createElement('script')
            script.src = `https://www.paypal.com/sdk/js?client-id=${config.public.paypal.clientId}&currency=EUR&components=buttons&locale=es_ES&disable-funding=paylater`
            script.async = true

            script.onload = () => {
                if (window.paypal) {
                    resolve(window.paypal)
                } else {
                    reject(new Error('PayPal SDK not loaded properly'))
                }
            }

            script.onerror = (error) => {
                console.error('Error loading PayPal script:', error)
                reject(error)
            }

            document.body.appendChild(script)
        })
    }

    return {
        provide: {
            paypal: {
                load: loadPayPalScript
            }
        }
    }
})
