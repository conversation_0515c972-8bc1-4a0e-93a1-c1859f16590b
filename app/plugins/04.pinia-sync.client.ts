import { useCartStore } from '~/stores/cart';

export default defineNuxtPlugin(() => {
    if (import.meta.client) {
        window.addEventListener('storage', (event) => {
            if (event.key === 'CartStore') {
                const cartStore = useCartStore();
                const newCart = event.newValue ? JSON.parse(event.newValue) : null;
                if (newCart) {
                    cartStore.$patch(newCart);
                }
            }
        });
    }
});
