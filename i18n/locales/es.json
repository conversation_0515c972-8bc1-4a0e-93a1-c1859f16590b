{"home": "<PERSON><PERSON>o", "brands": "<PERSON><PERSON>", "offers": "<PERSON><PERSON><PERSON>", "new": "Nuevos", "used": "Usados", "auth": "Autenticación", "login": "Inicio de Sesión", "register": "Registro", "verify": "Verificar", "email": "Correo Electrónico", "forgot password": "Recup<PERSON>r <PERSON>", "new password": "Nueva Contraseña", "profile": "<PERSON>", "disabled": "Desactivados", "error": {"notFound": "No encontrado.", "emailPasswordRequired": "El correo electrónico y la contraseña son obligatorios.", "registrationFailed": "Error al registrar.", "emailDuplicated": "Correo electrónico y registrado.", "emailAlreadyRegistered": "Este correo electrónico ya se encuentra registrado.", "internalServerError": "Error interno del servidor.", "invalidCredentials": "Las credenciales son incorrectas.", "userNotFound": "Usuario no encontrado.", "strategyNotImplemented": "Estrategia no implementada.", "methodNotImplemented": "Método no implementado.", "unknownError": "Ocurrió un error desconocido.", "uploadingFileMinIO": "Error al intentar subir el archivo a MinIO.", "uploadingFile": "Error al intentar subir el archivo.", "downloadingFile": "Ocurrió un error al intentar descargar el archivo.", "fileUploadMissing": "No se subió ningún archivo o falta el nombre del bucket.", "fileNameAndBucketRequired": "Se requiere el nombre del archivo y el bucket.", "errorOnGettingFile": "Ocurrió un error al intentar obtener el archivo.", "deletingFile": "Ocurrió un error al intentar eliminar el archivo.", "verifyFailed": "La verificación falló.", "badRequest": "Solicitud incorrecta.", "passwordUpdateFailed": "Falló la actualización de la contraseña.", "unverifiedEmail": "Correo electrónico no verificado.", "unauthorized": "No autorizado.", "FAQ": "Error recuperando el FAQ.", "updateProfileFailed": "Error al actualizar el perfil.", "phoneDuplicated": "Número celular ya registrado.", "reportDuplicated": "Ya envió un reporte de fraude para este anuncio.", "gettingPostData": "Error recuperando el post.", "tokenNotFound": "El token no se encontró en la respuesta", "tokenFetchFailed": "No se pudo obtener el token", "processingCatalog": "Error procesando el catálogo.", "insertMissingData": "<PERSON>al<PERSON> los datos a insertar.", "networkError": "Error de red.", "permissionDenied": "<PERSON><PERSON><PERSON> den<PERSON>ado.", "invalidJwt": "JWT inválido.", "graphQLOperationFailed": "Operación GraphQL fallida.", "validationError": "Error de validación.", "unexpectedError": "Error inesperado.", "whereClauseRequired": "Se requiere la cláusula where.", "invalidOperationType": "Tipo de operación inválido.", "processingCategory": "Error procesando las categorías.", "syncFailed": "Error en la sincronización.", "emptyCatalog": "El catálogo está vacío."}, "queue": {"error": {"answerNotBodyMessage": "La respuesta no contiene un body.", "addingProcessToQueue": "Error al agregar proceso a la cola xml-processing."}}, "store": {"error": {"gettingConfiguration": "Error recuperando y almacenando la configuración.", "invalidKey": "Llave inválida.", "gettingProducts": "Error recuperando los productos.", "gettingSales": "Error recuperando las ventas.", "dataMandatory": "Los datos son obligatorios.", "productsNotFound": "No se encontraron productos.", "gettingColors": "Error recuperando los colores.", "gettingSizes": "Error recuperando los tamaños.", "gettingMaterials": "Error recuperando los materiales.", "gettingCategories": "Error recuperando las categorías.", "gettingAddresses": "Error recuperando las direcciones.", "gettingPrints": "Error recuperando las impresiones.", "gettingUser": "Error recuperando el usuario.", "gettingMarking": "Error recuperando el marking."}}}