import { defineOrganization } from 'nuxt-schema-org/schema';

export default defineNuxtConfig({
  compatibilityDate: '2024-11-01',
  future: { compatibilityVersion: 4 },
  devtools: { enabled: true },
  modules: [
    '@nuxt/fonts',
    '@nuxt/image',
    '@nuxtjs/tailwindcss',
    '@primevue/nuxt-module',
    '@nuxtjs/i18n',
    '@nuxt/icon',
    '@nuxtjs/color-mode',
    'nuxt-arpix-db-connect',
    'nuxt-arpix-email-sender',
    '@pinia/nuxt',
    'pinia-plugin-persistedstate/nuxt',
    'nuxt-auth-utils',
    '@nuxtjs/seo',
    '@dargmuesli/nuxt-cookie-control'
  ],

  css: ["~/assets/css/main.css"],
  postcss: {
    plugins: {
      'postcss-import': {},
      tailwindcss: {},
      autoprefixer: {},
    },
  },
  tailwindcss: {
    config: {
      theme: {
        extend: {
          colors: {
            primary: {
              DEFAULT: "#ae060b",
              50: '#fff0f0',
              100: '#ffddde',
              200: '#ffc1c3',
              300: '#ff979a',
              400: '#ff5b60',
              500: '#ff282f',
              600: '#fa0810',
              700: '#e00209',
              800: '#ae060b',
              900: '#8f0d11',
              950: '#4f0002'
            },
            active: '#ae060b'
          }
        },
      },
    }
  },
  primevue: {
    importTheme: { from: '@/assets/themes/config.js' },
  },
  colorMode: {
    preference: 'light',
    fallback: 'light'
  },
  i18n: {
    bundle: {
      optimizeTranslationDirective: false, // Disable optimization
    },
    locales: [
      { code: 'es', language: 'es-ES', file: 'es.json' },
      // { code: 'en', language: 'en-US', file: 'en.json' },
    ],
    defaultLocale: 'es',
    lazy: true,
    strategy: 'no_prefix'
  },
  image: {
    format: ['webp'],
    provider: 'ipx',
    domains: ['enyes.es']
  },
  cookieControl: {
    locales: ['es'],
    isControlButtonEnabled: false
  },

  site: {
    url: 'https://ebppublicidad.xyz',
    name: 'EBP Publicidad',
    title: 'EBP Publicidad - Personalización Textil, Serigrafía, Bordado y Estampado',
    description: 'Especialistas en personalización textil con serigrafía, bordado y estampado. Ofrecemos camisetas, polos y sudaderas personalizadas con diseños de alta calidad. ¡Haz tu marca visible con EBP Publicidad!',
    defaultLocale: 'es',
  },
  schemaOrg: {
    identity: defineOrganization({
      '@type': ['Organization', 'Store', 'OnlineStore'],
      'name': 'EBP Publicidad',
      'logo': '/images/logo.webp',
    }),
  },
  seo: {
    meta: {
      title: 'EBP Publicidad - Personalización Textil, Serigrafía, Bordado y Estampado',
      description: 'Especialistas en personalización textil con serigrafía, bordado y estampado. Ofrecemos camisetas, polos y sudaderas personalizadas con diseños de alta calidad. ¡Haz tu marca visible con EBP Publicidad!',
    },
  },
  app: {
    head: {
      charset: 'utf-8',
      templateParams: {
        separator: '|',
      },
    },
  },
  sitemap: {
    autoI18n: false,
    includeAppSources: true,
    exclude: ['/auth/**', '/profile/**', '/cart/**', '/en/**'],
    sources: [`/api/enyes/store/product`],
    urls: async () => {
      const languages = ['esp'];
      return languages.map(lang => ({
        loc: `/catalogo`,
        changefreq: 'daily',
        priority: 0.8
      }));
    }
  },

  piniaPluginPersistedstate: {
    storage: 'localStorage',
  },

  dbConnect: {
    dataOrigin: 'hasura',
    hasura: {
      url: process.env.HASURA_URL || '',
      // headers: {
      //   'x-hasura-admin-secret': 'hasurapassword',
      // }
    },
    dataDebug: process.env.HASURA_DEBUG === 'true',
  },
  arpixEmailSender: {
    transport: 'smtp',
    defaultFrom: '"EBP Publicidad" <<EMAIL>>',
    smtp: {
      host: process.env.EMAIL_HOST || 'sandbox.smtp.mailtrap.io',
      port: parseInt(process.env.EMAIL_PORT || '465'),
      secure: (process.env.EMAIL_SECURE === 'true'),
      auth: {
        user: process.env.EMAIL_USER || '509931f12becb9',
        pass: process.env.EMAIL_PASS || 'd10d00d5f629e6',
      }
    },
    templates: {
      dir: 'server/emails/templates',
    },
  },

  runtimeConfig: {
    public: {
      site: {
        url: process.env.NUXT_SITE_URL || 'http://localhost:3001'
      },
      paypal: {
        clientId: process.env.PAYPAL_CLIENT_ID || ''
      }
    },
    paypal: {
      clientId: process.env.PAYPAL_CLIENT_ID || '',
      clientSecret: process.env.PAYPAL_CLIENT_SECRET || '',
      mode: process.env.PAYPAL_MODE || 'sandbox',
    },
    session: {
      name: 'arpix_enyes_auth_session',
      maxAge: 60 * 60 * 24 * 7, // 1 week
      password: process.env.NUXT_SESSION_PASSWORD || 'default_session_password'
    },
    auth: {
      register: {
        verifyRequired: (process.env.AUTH_REGISTER_VERIFY_REQUIRED === 'true') || false
      }
    },
    site: {
      url: process.env.NUXT_SITE_URL || 'http://localhost:3001'
    },
    redis: {
      host: process.env.REDIS_HOST,
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.NODE_ENV === 'production' ? process.env.REDIS_PASSWORD : undefined
    },
    enyes: {
      url: process.env.ENYES_URL || '',
      userId: process.env.ENYES_USER_ID || '',
      verifyKey: process.env.ENYES_VERIFY_KEY || 'verify-key'
    }
  }
})