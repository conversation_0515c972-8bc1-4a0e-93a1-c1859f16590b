import { SignJWT, jwtVerify } from 'jose';
import { loadOrGenerateKeys } from '../tools/keys';
import bcrypt from 'bcryptjs';

export const generateTokens = async (userId: string, role: string) => {
    const { privateJWK } = await loadOrGenerateKeys();

    const accessToken = await new SignJWT({
        sub: userId,
        "https://hasura.io/jwt/claims": {
            "x-hasura-allowed-roles": [role],
            "x-hasura-default-role": role,
            "x-hasura-user-id": userId
        }
    })
    .setProtectedHeader({ alg: 'RS256' })
    .setIssuedAt()
    .setExpirationTime('15m')
    .sign(privateJWK);

    // const refreshToken = await new SignJWT({
    //     sub: userId,
    // })
    // .setProtectedHeader({ alg: 'RS256' })
    // .setIssuedAt()
    // .setExpirationTime('7d')
    // .sign(privateJWK);

    return { accessToken };
}

export const verifyTokenJwt = async (token:string) => {
    const { privateJWK } = await loadOrGenerateKeys();

    try {
        const { payload } = await jwtVerify(token, privateJWK);
        return payload;
    } catch (err: any) {
        console.error('Token is invalid or expired', err);
        return null;
    }
}

export const getHashedPassword = async (password: string): Promise<string> => {
    const saltRounds = 10;
    const salt = await bcrypt.genSalt(saltRounds);
    const hashedPassword = await bcrypt.hash(password, salt);
    return hashedPassword;
};

export const comparePassword = async (password: string, hash: string): Promise<boolean> => {
    return await bcrypt.compare(password, hash);
};
