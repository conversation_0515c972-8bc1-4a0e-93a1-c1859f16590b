export const getError = (err: any) => {
    const statusCodeMap: Record<string, number> = {
        'error.invalidJwt': 401,
        'error.permissionDenied': 403,
        'error.validationError': 400
    };

    const errorMessage = err instanceof Error ? err.message : 'error.unexpectedError';
    const statusCode = statusCodeMap[errorMessage] || 500;

    return createError({
        statusCode,
        statusMessage: errorMessage,
        data: { error: { code: errorMessage, details: undefined } }
    });
}
