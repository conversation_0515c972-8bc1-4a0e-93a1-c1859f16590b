export interface DatabaseInterface {
    getConfig(): Promise<any>;
    getCategory(): Promise<any>;
    getColor(): Promise<any>;
    getSize(): Promise<any>;
    getProducts(data: string): Promise<any>;
    getProduct(id: string): Promise<any>;
    getPrints(): Promise<any>;
    savePurchasePayment(data: any): Promise<any>;
}

export interface parametersLogin {
    email?: string;
    password?: string;
    strategy: string;
}

export interface registerPayload {
    email: string;
    password: string;
    username?: string;
    phone?: string;
    first_name?: string;
    last_name?: string;
    email_verify?: boolean;
    email_code_verify?: string;
    email_verify_at?: Date;
    phone_verify?: boolean;
    phone_code_verify?: string;
    phone_verify_at?: Date;
    roles?: Object;
    discount?: Object;
}

export interface parametersRegister {
    payload: registerPayload;
    strategy: string;
    providerId?: string;
}

export interface verifyOrigin {
    id: string;
    code: string;
    origin: 'email' | 'phone'
}

export interface parametersfindUserById {
    userId: string;
}

export interface parametersfindUserByEmail {
    email: string;
}

export interface parametersfindUserByPhone {
    phone: string;
}

export interface ForgotPasswordParameters {
    email: string;
    password_code_recovery: string;
}

export interface UpdatePasswordParameters {
    email: string;
    code: string;
    password: string;
}
