import { 
    parameters<PERSON>ogin,
    parametersfindUser<PERSON>y<PERSON><PERSON>,
    parametersfindUserById,
    parametersfindUserByPhone,
    parametersRegister,
    verifyOrigin,
    ForgotPasswordParameters,
    UpdatePasswordParameters
} from "../../interface/database.interface"
import { generateTokens, comparePassword, getHashedPassword } from "../../tools/auth"
import { add24Hours } from "../../tools/utils"
import { createError } from 'h3'

const getUserByEmail = async (email: string) => {
    const { accessToken } = await generateTokens('system', 'system')
    const db = useDbConnector()
    const result: any = await db.get('user', {
        select: `
            id
            username
            email
            password
            first_name
            last_name
            phone
            email_verify
            email_code_verify
            discount
            roles {
                role_code
            }
        `,
        where: {
            email: { _eq: email }
        }}, accessToken
    )

    return result?.user[0] || null
}

export const findUserByEmail = async (parameters: parametersfindUserByEmail) => {
    const { email } = parameters
    const user: any = await getUserByEmail(email)
    delete user.password
    return user
}

export const login = async (parameters: parametersLogin) => {
    const { strategy, email, password } = parameters

    if (strategy !== 'local') {
        throw new Error("error.strategyNotImplemented")
    }

    if (!email || !password) {
        throw new Error("error.emailPasswordRequired")
    }

    const user: any = await getUserByEmail(email)
    if (!user || !user.id) {
        throw new Error("error.invalidCredentials")
    }

    const isValid = await comparePassword(password, user.password)
    if (!isValid) {
        throw new Error("error.invalidCredentials")
    }
    delete user.password

    return user
}

export const register = async (parameters: parametersRegister) => {
    const { payload, strategy } = parameters

    if (strategy !== 'local') {
        throw new Error("error.strategyNotImplemented")
    }

    const { accessToken } = await generateTokens('system', 'system')
    const hash = await getHashedPassword(payload.password)
    payload.password = hash
    payload.email_verify_at = add24Hours()
    payload.roles = {data: {role_code: "user"}}
    const db = useDbConnector()
    const result: any = await db.insert('user',
        payload as unknown as Record<string, unknown>,
        null,
        `returning {
            id
            email
            email_code_verify
        }`,
        accessToken
    )

    return result?.insert_user?.returning[0] || null
}

export const verify = async(parameters: verifyOrigin): Promise<any> => {
    const { id, code, origin }: any = parameters
    const { accessToken } = await generateTokens('system', 'system')
    const db = useDbConnector()
    let query = ''

    if (origin == 'email') {
        query = `
            mutation VerifyEmail($id: uuid, $code: String) {
                update_user(where: {id: {_eq: $id}, email_code_verify: {_eq: $code}}, _set: {email_verify: true, email_verify_at: "now()", email_code_verify: null}) {
                    affected_rows
                }
            }
        `;
    } else {
        query = `
            mutation VerifyPhone($id: uuid, $code: String) {
                update_user(where: {id: {_eq: $id}, phone_code_verify: {_eq: $code}}, _set: {phone_verify: true, phone_verify_at: "now()", phone_code_verify: null}) {
                    affected_rows
                }
            }
        `;
    }

    try {
        const variables = { id, code }
        const graphqlClient = db.getClient(accessToken)
        const response: any = await graphqlClient.request(query, variables)

        return response.update_user
    } catch (err: any) {
        console.error("Error verify code:", err)
        throw new Error("error.verifyFailed")
    }
}

export const checkUserByEmail = async (parameters: parametersfindUserByEmail): Promise<any> => {
    const { accessToken } = await generateTokens('system', 'system')
    const db = useDbConnector()
    const response: any = await db.get('user', {
        select: `
            id
            email_verify
        `,
        where: {
            email: { _eq: parameters.email }
        }}, accessToken)

    if (response.user.length === 0) {
        throw createError({
            statusCode: 404,
            message: "error.userNotFound",
        })
    }

    return { id: response.user[0].id, verify: response.user[0].email_verify }
}

export const checkUserByPhone = async (parameters: parametersfindUserByPhone): Promise<any> => {
    const { accessToken } = await generateTokens('system', 'system')
    const db = useDbConnector()

    const response: any = await db.get('user', {
        select: `
            id
            phone_verify
        `,
        where: {
            phone: { _eq: parameters.phone }
        }}, accessToken
    )

    if (response.user.length === 0) {
        throw createError({
            statusCode: 404,
            message: "error.userNotFound",
        })
    }

    return { id: response.user[0].id, verify: response.user[0].phone_verify }
}

export const forgotPassword = async (parameters: ForgotPasswordParameters) => {
    const { email, password_code_recovery } = parameters
    if (!email || !password_code_recovery) throw new Error("error.badRequest")
    
    const { accessToken } = await generateTokens('system', 'system')
    const db = useDbConnector()
    const graphqlClient = db.getClient(accessToken)
    const password_recovery_at = add24Hours()

    const query = `
        mutation updatePasswordCodeRecovery($email: String, $password_code_recovery: String, $password_recovery_at: timestamptz) {
            update_user(where: {email: {_eq: $email}}, _set: {password_code_recovery: $password_code_recovery, password_recovery_at: $password_recovery_at}) {
                affected_rows
            }
        }
    `

    try {
        const variables = { email, password_code_recovery, password_recovery_at }
        const response: any = await graphqlClient.request(query, variables)

        return response?.update_user
    } catch (err: any) {
        console.error("Error verify code:", err)
        throw new Error("error.verifyFailed")
    }
}

export const updatePassword = async (parameters: UpdatePasswordParameters) => {
    const { email, password, code } = parameters
    if (!email || !password || !code) throw new Error("error.badRequest")
    
    const { accessToken } = await generateTokens('system', 'system')
    const db = useDbConnector()
    const graphqlClient = db.getClient(accessToken)
    const hash = await getHashedPassword(password)
    const newPassword = hash

    const query = `
        mutation updatePassword($email: String, $newPassword: String, $code: String) {
            update_user(where: {email: {_eq: $email}, password_code_recovery: {_eq: $code}}, _set: {password: $newPassword}) {
                affected_rows
            }
        }
    `

    try {
        const variables = { email, newPassword, code }
        const response: any = await graphqlClient.request(query, variables)

        return response?.update_user
    } catch (err: any) {
        console.error("Error updating password:", err)
        throw new Error("error.passwordUpdateFailed")
    }
}

export const updateProfile = async(parameters: any): Promise<any> => {
    const { accessToken } = await generateTokens(parameters?.user?.id, parameters?.user?.roles[0].role_code)
    const db = useDbConnector()
    const response: any = await db.update('user', parameters.payload.data, { id: { _eq: parameters.payload.id } }, `
        returning {
            id
            email
            first_name
            phone
            roles {
                role_code
            }    
        }
    `, accessToken)

    return response?.update_user?.returning?.[0] || {}
}

export const getAddresses = async(parameters: any): Promise<any> => {
    const { accessToken } = await generateTokens(parameters?.user?.id, parameters?.user?.roles[0].role_code)
    const db = useDbConnector()
    const response: any = await db.get('user', {
        select: `
            id
            email
            addresses_aggregate {
                aggregate {
                    count
                }
                nodes {
                    id
                    province
                    city
                    address_1
                    address_2
                    zip_code
                    created_at
                }
            }
        `,
        where: { id: { _eq: parameters.payload.id } } 
    }, accessToken )

    return response?.user?.[0] || {}
}

export const insertAddress = async(parameters: any): Promise<any> => {
    const { accessToken } = await generateTokens(parameters?.user?.id, parameters?.user?.roles[0].role_code)
    const db = useDbConnector()
    const response: any = await db.insert('user_addresses',
        parameters.payload.data,
        null,
        `returning {
            id
            province
            city
            address_1
            address_2
            zip_code
            created_at
        }`,
        accessToken
    )

    return response?.insert_user_addresses?.returning?.[0] || {}
}

export const deleteAddress = async(parameters: any): Promise<any> => {
    const { accessToken } = await generateTokens(parameters?.user?.id, parameters?.user?.roles[0].role_code)
    const db = useDbConnector()
    const response: any = await db.delete('user_addresses',
        { id: { _eq: parameters.payload.id } },
        `affected_rows`,
        accessToken
    )

    return response?.delete_user_addresses || null
}

export const getSales = async (parameters: any): Promise<any> => {
    const { accessToken } = await generateTokens(parameters?.user?.id, parameters?.user?.roles[0].role_code)
    const db = useDbConnector()
    const response: any = await db.get('user', {
        select: `
            id
            email
            sales_aggregate(where: {origin: {_eq: "enyes"}}, order_by: {created_at: desc}) {
                aggregate {
                    count
                }
                nodes {
                    id
                    cart_id
                    quantity_items
                    amount
                    discount
                    shipment
                    shipment_type
                    tax
                    total
                    payload
                    created_at
                    status
                    expedition
                    expedition_url
                    address {
                        id
                        address_1
                        address_2
                        zip_code
                        province
                        city
                    }
                    payment {
                        type
                        invoice_id
                    }
                }
            }
        `,
        where: { id: { _eq: parameters.payload.id } }
    }, accessToken)

    return response?.user?.[0] || {}
}
