import { saveColors, saveSizes, saveProducts, saveProductCategory, saveVariants, saveMarking } from './products';

const fixInvalidJson = (text: string): any => {
    const fixed = text.replace(/[\r\n\t\x00-\x1F\x7F]+/g, ' ')
    try {
        return JSON.parse(fixed)
    } catch {
        return { raw: text, error: 'No se pudo parsear' }
    }
}

const stringToHex = (str: string): string => {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
        hash = str.charCodeAt(i) + ((hash << 5) - hash)
    }
    return '#' + ((hash & 0xFFFFFF) >>> 0).toString(16).padStart(6, '0')
}

const slugifyColor = (str: string): string => 
    str.normalize('NFD')
        .replace(/\p{Diacritic}/gu, '')
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '')

const processProductData = (obj: any, keys: string[], colorsMap: Map<string, any>, sizesMap: Map<string, any>) => {
    const key = keys[0]
    if (obj.combinations) {
        Object.values(obj.combinations).forEach((comb: any) => {
            if (!comb?.attributes) return
            const { color, talla } = comb.attributes
            if (color) {
                const slug = slugifyColor(color)
                if (!colorsMap.has(slug)) {
                    colorsMap.set(slug, { id: slug, name: color, hexcode: stringToHex(color) })
                }
            }
            if (talla) {
                const sizeSlug = slugifyColor(talla)
                if (!sizesMap.has(sizeSlug)) {
                    sizesMap.set(sizeSlug, { id: sizeSlug, name: talla })
                }
            }
        })
    }
    const { features, name, description, custom_features, images, categories } = obj
    const material = features?.Material || null
    const productName = name?.['1'] || null
    const productDescription = description?.['1'] || null
    const {
        page = { '1': null },
        text = { '1': null },
        url_montaje = { '1': null },
        outer_box_unit = 0,
        inner_box_unit = 0,
        measure_article = null
    } = custom_features || {}
    let image = null
    if (Array.isArray(images)) {
        const imgObj = images.find((img: any) => 
            img.url && img.url.split('/').pop()?.split('.')[0] === key
        )
        image = imgObj?.url || null
    }
    const slug = productName && key ? `${slugifyColor(productName)}-${key}` : null
    const product = {
        id: obj.id || key,
        slug,
        active: obj.active === 1,
        allow_sale_without_stock: obj.allow_sale_without_stock === 1,
        ean: obj.ean,
        weight: obj.weight,
        width: obj.width,
        depth: obj.depth,
        height: obj.height,
        minimal_quantity: obj.minimal_quantity,
        material,
        name: productName,
        description: productDescription,
        page: page['1'],
        text: text['1'],
        url_montaje: url_montaje['1'],
        image,
        outer_box_unit: outer_box_unit ?? 0,
        inner_box_unit: inner_box_unit ?? 0,
        measure_article
    }
    const productCategories = Array.isArray(categories) 
        ? categories.map((catId: string) => ({ product_id: product.id, category_id: catId }))
        : []
    return { product, productCategories }
}

const processVariants = (obj: any, keys: string[]) => {
    const variants: any[] = []
    const key = keys[0]
    if (!obj.combinations) return variants
    Object.entries(obj.combinations).forEach(([combKey, comb]: [string, any]) => {
        if (!comb?.attributes) return
        const { color = '', talla = '' } = comb.attributes
        const color_id = color ? slugifyColor(color) : null
        const size_id = talla ? slugifyColor(talla) : null
        const url_360 = obj.custom_features?.url_360?.[combKey]?.['1'] || null
        let image = null
        let legend = null
        if (Array.isArray(obj.images)) {
            const imgObj = obj.images.find((img: any) => {
                if (!img.url) return false
                const baseName = img.url.split('/').pop()?.split('.')[0]
                return baseName?.toLowerCase() === combKey.toLowerCase()
            })
            if (imgObj) {
                image = imgObj.url
                legend = typeof imgObj.legend === 'string' 
                    ? imgObj.legend 
                    : imgObj.legend?.['1'] || Object.values(imgObj.legend || {})[0] || null
            }
        }
        variants.push({
            id: combKey,
            product_id: key,
            color_id,
            size_id,
            url_360,
            ean: comb.ean || null,
            image,
            legend
        })
    })
    return variants
}

export async function processProducts({ url, uid }: { url: string, uid: string }, job?: any) {
    try {
        const catalogRes = await fetch(`${url}/w-catalogo.pro?W-USU=${uid}`)
        if (!catalogRes.ok) throw new Error('Error al consultar catálogo')
        const { products: ids = [] } = await catalogRes.json()
        const limit = 10 // Reducir concurrencia para evitar bloqueos
        const colorsMap = new Map<string, any>()
        const sizesMap = new Map<string, any>()
        const products: any[] = []
        const productCategories: any[] = []
        const variants: any[] = []
        const marking: any[] = []
        const markingSetGlobal = new Set<string>()
        for (let i = 0; i < ids.length; i += limit) {
            if (job && typeof job.updateProgress === 'function') {
                const percent = Math.round((i / ids.length) * 100)
                job.updateProgress(percent)
                console.log(`[BullMQ][processProducts] Progreso: ${percent}% (${i}/${ids.length})`)
            } else {
                const percent = Math.round((i / ids.length) * 100)
                console.log(`[processProducts] Progreso: ${percent}% (${i}/${ids.length})`)
            }
            const chunk = ids.slice(i, i + limit)
            const results = await Promise.allSettled(
                chunk.map(async (pid: string) => {
                    const productUrl = `${url}/w-product.pro?W-USU=${uid}&w-art=${pid}`
                    try {
                        const res = await fetch(productUrl, {
                            headers: {
                                'User-Agent': 'Mozilla/5.0 (compatible; Node.js fetch)'
                            }
                        })
                        if (!res.ok) throw new Error(`Error al consultar producto: ${productUrl}`)
                        const buffer = await res.arrayBuffer()
                        const text = new TextDecoder('latin1').decode(buffer)
                        const fixed = fixInvalidJson(text)
                        if (!fixed?.product || typeof fixed.product !== 'object') return null
                        const keys = Object.keys(fixed.product)
                        if (keys.length === 0) return null
                        const obj = fixed.product[keys[0]]
                        if (!obj || Object.keys(obj).length === 0) return null
                        const { product, productCategories: cats } = processProductData(obj, keys, colorsMap, sizesMap)
                        const productVariants = processVariants(obj, keys)
                        return { product, productCategories: cats, variants: productVariants, obj }
                    } catch (err) {
                        console.error(`[processProducts] Error al procesar producto ${pid}:`, err)
                        return null
                    }
                })
            )
            results.forEach(result => {
                if (result.status === 'fulfilled' && result.value) {
                    const { product, productCategories: cats, variants: vars, obj } = result.value
                    products.push(product)
                    productCategories.push(...cats)
                    variants.push(...vars)
                    const area: any[] = []
                    const markingAreas: Record<string, any[]> = {}
                    if (product && product.id && obj && obj.marcajes && typeof obj.marcajes === 'object') {
                        for (const [markingId, markingObj] of Object.entries(obj.marcajes)) {
                            const markingKey = `${markingId}-${product.id}`
                            for (const [fieldId, fieldObj] of Object.entries(markingObj as Record<string, any>)) {
                                const fieldData = fieldObj['1']
                                if (fieldData && typeof fieldData === 'object') {
                                    const areaId = fieldId
                                    const name = fieldData.name || null
                                    const measures = fieldData.measures || null
                                    let image = null
                                    if (obj.images_marcajes && typeof obj.images_marcajes === 'object') {
                                        image = obj.images_marcajes[areaId] || null
                                    }
                                    if (!area.find(a => a.id === areaId)) {
                                        area.push({ id: areaId, name, measures, image, markingId })
                                    }
                                    if (!markingAreas[markingId]) markingAreas[markingId] = []
                                    if (!markingAreas[markingId].find(a => a.id === areaId)) {
                                        markingAreas[markingId].push({ id: areaId, name, measures, image })
                                    }
                                }
                            }
                            if (!markingSetGlobal.has(markingKey)) {
                                markingSetGlobal.add(markingKey)
                                marking.push({ print_id: markingId, product_id: product.id, area: markingAreas[markingId] || [] })
                            }
                        }
                    }
                }
            })
            // Pequeño delay entre lotes para evitar rate-limit
            await new Promise(res => setTimeout(res, 300))
        }
        if (job && typeof job.updateProgress === 'function') {
            job.updateProgress(100)
            console.log(`[BullMQ][processProducts] Progreso: 100% (${ids.length}/${ids.length})`)
        } else {
            console.log(`[processProducts] Progreso: 100% (${ids.length}/${ids.length})`)
        }
        const colors = Array.from(colorsMap.values())
        const sizes = Array.from(sizesMap.values())
        try {
            await Promise.all([
                saveColors(colors),
                saveSizes(sizes),
                saveProducts(products)
            ])
            await Promise.all([
                saveProductCategory(productCategories),
                saveVariants(variants)
            ])
            await saveMarking(marking)
        } catch (err) {
            console.error('[processProducts] Error al guardar en base de datos:', err)
            console.error('[processProducts] Payload colors:', JSON.stringify(colors).slice(0, 1000))
            console.error('[processProducts] Payload sizes:', JSON.stringify(sizes).slice(0, 1000))
            console.error('[processProducts] Payload products:', JSON.stringify(products).slice(0, 1000))
            console.error('[processProducts] Payload productCategories:', JSON.stringify(productCategories).slice(0, 1000))
            console.error('[processProducts] Payload variants:', JSON.stringify(variants).slice(0, 1000))
            console.error('[processProducts] Payload marking:', JSON.stringify(marking).slice(0, 1000))
            throw err
        }
        return { 
            product: products, 
            colors, 
            sizes, 
            variant: variants, 
            product_category: productCategories,
            marking
        }
    } catch (err) {
        console.error('[processProducts] Error general:', err)
        throw err
    }
}
