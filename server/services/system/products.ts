import { generateTokens } from "../../tools/auth"

interface Color {
    id: string
    name: string
    hexcode: string
}

interface Size {
    id: string
    name: string
}

interface Product {
    id: string
    active: boolean
    allow_sale_without_stock: boolean
    ean: string
    weight: number
    width: number
    depth: number
    height: number
    minimal_quantity: number
    material: string
    name: string
    description: string
    page: string
    text: string
    url_montaje: string
    image: string
    outer_box_unit: string
    inner_box_unit: string
    measure_article: string
}

export const saveColors = async (colors: Color[]) => {
    const { accessToken } = await generateTokens('system', 'system')
    const db = useDbConnector()

    const result = await db.insert(
        'enyes_color',
        colors.map(c => ({ ...c })),
        {
            constraint: 'enyes_color_pkey',
            update_columns: []
        },
        `returning {
            id
        }`,
        accessToken
    )
    return result
}

export const saveSizes = async (sizes: Size[]) => {
    const { accessToken } = await generateTokens('system', 'system')
    const db = useDbConnector()

    const result = await db.insert(
        'enyes_size',
        sizes.map(s => ({ ...s })),
        {
            constraint: 'enyes_size_pkey',
            update_columns: []
        },
        `returning {
            id
        }`,
        accessToken
    )
    return result
}

export const saveProducts = async (products: Product[]) => {
    const { accessToken } = await generateTokens('system', 'system')
    const db = useDbConnector()

    const result = await db.insert(
        'enyes_product',
        products.map(p => ({ ...p })),
        {
            constraint: 'enyes_product_pkey',
            update_columns: ['active']
        },
        `returning {
            id
        }`,
        accessToken
    )
    return result
}

export const saveProductCategory = async (product_category: any[]) => {
    const { accessToken } = await generateTokens('system', 'system')
    const db = useDbConnector()
    const categoriesResult: any = await db.get('enyes_category', {
        select: ['id']
    }, accessToken)
    const categoryList = Array.isArray(categoriesResult) ? categoriesResult : (categoriesResult.enyes_category || [])
    const validCategoryIds = new Set(categoryList.map((cat: any) => cat.id))
    const validProductCategory = product_category.filter(pc => validCategoryIds.has(pc.category_id))
    if (validProductCategory.length === 0) return { affected_rows: 0 }
    const result = await db.insert(
        'enyes_product_category',
        validProductCategory.map(pc => ({ ...pc })),
        {
            constraint: 'enyes_product_category_pkey',
            update_columns: []
        },
        `returning {
            product_id
            category_id
        }`,
        accessToken
    )
    return result
}

export const saveVariants = async (variants: any[]) => {
    const { accessToken } = await generateTokens('system', 'system')
    const db = useDbConnector()

    const result = await db.insert(
        'enyes_product_variant',
        variants.map(v => ({ ...v })),
        {
            constraint: 'enyes_product_variant_pkey',
            update_columns: ['url_360', 'ean', 'image', 'legend']
        },
        `returning {
            id
        }`,
        accessToken
    )
    return result
}

export const updatePrices = async (prices: any[]) => {
    const { accessToken } = await generateTokens('system', 'system')
    const db = useDbConnector()
    let total = 0
    for (const p of prices) {
        await db.update(
            'enyes_product_variant',
            { price_1: p.price_1 },
            { id: { _eq: p.id } },
            `returning {
                id
            }`,
            accessToken
        )
        total++
    }
    return { affected_rows: total }
}

export const updateStock = async (stockArr: { id: string, stock: number }[]) => {
    const { accessToken } = await generateTokens('system', 'system')
    const db = useDbConnector()
    let total = 0
    for (const s of stockArr) {
        if (typeof s.stock !== 'number') continue
        await db.update(
            'enyes_product_variant',
            { stock: s.stock },
            { id: { _eq: s.id } },
            `returning {
                id
            }`,
            accessToken
        )
        total++
    }
    return { affected_rows: total }
}

export const saveMarking = async (marking: any[]) => {
    const { accessToken } = await generateTokens('system', 'system')
    const db = useDbConnector()
    const printsResult: any = await db.get('enyes_print', { select: ['id'] }, accessToken)
    const printList = Array.isArray(printsResult) ? printsResult : (printsResult.enyes_print || [])
    const validPrintIds = new Set(printList.map((p: any) => p.id))
    const validMarking = marking.filter(m => validPrintIds.has(m.print_id))
    if (validMarking.length === 0) return { affected_rows: 0 }
    const result = await db.insert(
        'enyes_product_print',
        validMarking.map(m => ({
            print_id: m.print_id,
            product_id: m.product_id,
            area: Array.isArray(m.area) ? m.area : []
        })),
        {
            constraint: 'enyes_product_print_pkey',
            update_columns: ['area']
        },
        `returning {
            id
        }`,
        accessToken
    )
    return result
}

export const getProducts = async (query: any) => {
    const { accessToken } = await generateTokens('store', 'store')
    const db = useDbConnector()
    const variables = { filter: query.filter, order_by: query.order_by, limit: query.limit, offset: query.offset }
    const graphqlClient = db.getClient(accessToken)
    const q = `
        query getProducts($filter: enyes_product_bool_exp, $limit: Int, $offset: Int, $order_by: [enyes_product_order_by!]) {
            enyes_product(where: $filter, limit: $limit, offset: $offset, order_by: $order_by) {
                id
                name
                description
                ean
                slug
                image
                variants {
                    image
                    url_360
                    price_1
                }
            }
            enyes_product_aggregate(where: $filter) {
                aggregate {
                    count
                }
            }
        }
    `
    const result: any = await graphqlClient.request(q, variables)

    if (!result?.enyes_product) {
        throw new Error("store.error.productsNotFound");
    }

    return result
}

export const getMaterials = async (query: any) => {
    try {
        const { accessToken } = await generateTokens('store', 'store')
        const db = useDbConnector()

        let filterObj = query.filter
        if (typeof filterObj === 'string') {
            try {
                filterObj = JSON.parse(filterObj)
            } catch (e) {
                filterObj = {}
            }
        }

        let orderBy = query.order_by
        if (!orderBy || typeof orderBy !== 'object') {
            orderBy = [{ material: 'asc' }]
        }

        const variables = { where: filterObj, order_by: orderBy }
        const graphqlClient = db.getClient(accessToken)
        const result: any = await graphqlClient.request(`
            query GetMaterials($where: enyes_product_bool_exp!, $order_by: [enyes_product_order_by!]) {
                enyes_product(where: $where, order_by: $order_by, distinct_on: material) {
                    material
                }
            }
        `, variables)

        return Array.isArray(result?.enyes_product) ? result.enyes_product.map((m: any) => m.material).filter((mat: string | null | undefined) => !!mat) : []
    } catch (err: any) {
        console.error('Error getting materials:', err)
        throw new Error('store.error.gettingMaterials')
    }
}

export const getProduct = async (slug: string) => {
    const { accessToken } = await generateTokens('store', 'store')
    const db = useDbConnector()
    const result: any = await db.get('enyes_product', {
        select: `
            id
            name
            description
            ean
            slug
            image
            allow_sale_without_stock
            depth
            height
            width
            weight
            material
            inner_box_unit
            measure_article
            minimal_quantity
            outer_box_unit
            page
            text
            url_montaje
            categories {
                category {
                    id
                    name
                    slug
                }
            }
            variants {
                id
                ean
                image
                legend
                stock
                url_360
                color {
                    id
                    name
                    hexcode
                }
                size {
                    id
                    name
                }
                limit_1
                price_1
            }
            prints {
                id
                area
                print {
                    id
                    name
                    description
                    cliche
                    clicherep
                    minjob
                    price1
                    price2
                    price3
                    price4
                    price5
                    amountunder1
                    amountunder2
                    amountunder3
                    amountunder4
                    amountunder5
                }
            }
        `,
        where: { slug: { _eq: slug } }
    }, accessToken)

    return result?.enyes_product?.[0] || null
}

export const getColors = async () => {
    const { accessToken } = await generateTokens('store', 'store')
    const db = useDbConnector()
    const result: any = await db.get('enyes_color', {
        select: `
            id
            name
            hexcode
        `
    }, accessToken)

    return result?.enyes_color || []
}

export const getSizes = async () => {
    const { accessToken } = await generateTokens('store', 'store')
    const db = useDbConnector()
    const result: any = await db.get('enyes_size', {
        select: `
            id
            name
        `
    }, accessToken)

    return result?.enyes_size || []
}

// Helper functions for product processing
const fixInvalidJson = (text: string): any => {
    const fixed = text.replace(/[\r\n\t\x00-\x1F\x7F]+/g, ' ')
    try {
        return JSON.parse(fixed)
    } catch {
        return { raw: text, error: 'No se pudo parsear' }
    }
}

const stringToHex = (str: string): string => {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
        hash = str.charCodeAt(i) + ((hash << 5) - hash)
    }
    return '#' + ((hash & 0xFFFFFF) >>> 0).toString(16).padStart(6, '0')
}

const slugifyColor = (str: string): string =>
    str.normalize('NFD')
        .replace(/\p{Diacritic}/gu, '')
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '')

const processProductData = (obj: any, keys: string[], colorsMap: Map<string, any>, sizesMap: Map<string, any>) => {
    const key = keys[0]

    if (obj.combinations) {
        Object.values(obj.combinations).forEach((comb: any) => {
            if (!comb?.attributes) return

            const { color, talla } = comb.attributes

            if (color) {
                const slug = slugifyColor(color)
                if (!colorsMap.has(slug)) {
                    colorsMap.set(slug, { id: slug, name: color, hexcode: stringToHex(color) })
                }
            }

            if (talla) {
                const sizeSlug = slugifyColor(talla)
                if (!sizesMap.has(sizeSlug)) {
                    sizesMap.set(sizeSlug, { id: sizeSlug, name: talla })
                }
            }
        })
    }

    const { features, name, description, custom_features, images, categories } = obj
    const material = features?.Material || null
    const productName = name?.['1'] || null
    const productDescription = description?.['1'] || null

    // Custom features
    const {
        page = { '1': null },
        text = { '1': null },
        url_montaje = { '1': null },
        outer_box_unit = 0,
        inner_box_unit = 0,
        measure_article = null
    } = custom_features || {}

    // Imagen principal
    let image = null
    if (Array.isArray(images)) {
        const imgObj = images.find((img: any) =>
            img.url && img.url.split('/').pop()?.split('.')[0] === key
        )
        image = imgObj?.url || null
    }

    const slug = productName && key ? `${slugifyColor(productName)}-${key}` : null

    const product = {
        id: obj.id || key,
        slug,
        active: obj.active === 1,
        allow_sale_without_stock: obj.allow_sale_without_stock === 1,
        ean: obj.ean,
        weight: obj.weight,
        width: obj.width,
        depth: obj.depth,
        height: obj.height,
        minimal_quantity: obj.minimal_quantity,
        material,
        name: productName,
        description: productDescription,
        page: page['1'],
        text: text['1'],
        url_montaje: url_montaje['1'],
        image,
        outer_box_unit: outer_box_unit ?? 0,
        inner_box_unit: inner_box_unit ?? 0,
        measure_article
    }

    const productCategories = Array.isArray(categories)
        ? categories.map((catId: string) => ({ product_id: product.id, category_id: catId }))
        : []

    return { product, productCategories }
}

const processVariants = (obj: any, keys: string[]) => {
    const variants: any[] = []
    const key = keys[0]

    if (!obj.combinations) return variants

    Object.entries(obj.combinations).forEach(([combKey, comb]: [string, any]) => {
        if (!comb?.attributes) return

        const { color = '', talla = '' } = comb.attributes
        const color_id = color ? slugifyColor(color) : null
        const size_id = talla ? slugifyColor(talla) : null

        // URL 360
        const url_360 = obj.custom_features?.url_360?.[combKey]?.['1'] || null

        // Imagen y legend
        let image = null
        let legend = null

        if (Array.isArray(obj.images)) {
            const imgObj = obj.images.find((img: any) => {
                if (!img.url) return false
                const baseName = img.url.split('/').pop()?.split('.')[0]
                return baseName?.toLowerCase() === combKey.toLowerCase()
            })

            if (imgObj) {
                image = imgObj.url
                legend = typeof imgObj.legend === 'string'
                    ? imgObj.legend
                    : imgObj.legend?.['1'] || Object.values(imgObj.legend || {})[0] || null
            }
        }

        variants.push({
            id: combKey,
            product_id: key,
            color_id,
            size_id,
            url_360,
            ean: comb.ean || null,
            image,
            legend
        })
    })

    return variants
}

// Main function to process products data (moved from the endpoint)
export const processProductsData = async (config: any) => {
    const { url, userId: uid } = config

    // 1. List of product IDs
    const catalogRes = await fetch(`${url}/w-catalogo.pro?W-USU=${uid}`)
    if (!catalogRes.ok) throw new Error('Error al consultar catálogo')

    const { products: ids = [] } = await catalogRes.json()

    const limit = 50 // Batch size incremented
    const colorsMap = new Map<string, any>()
    const sizesMap = new Map<string, any>()
    const products: any[] = []
    const productCategories: any[] = []
    const variants: any[] = []
    const marking: any[] = []
    const markingSetGlobal = new Set<string>()

    // 2. Product data
    for (let i = 0; i < ids.length; i += limit) {
        const chunk = ids.slice(i, i + limit)

        const results = await Promise.allSettled(
            chunk.map(async (pid: string) => {
                const res = await fetch(`${url}/w-product.pro?W-USU=${uid}&w-art=${pid}`)
                if (!res.ok) return null

                const buffer = await res.arrayBuffer()
                const text = new TextDecoder('latin1').decode(buffer)
                const fixed = fixInvalidJson(text)

                if (!fixed?.product || typeof fixed.product !== 'object') return null

                const keys = Object.keys(fixed.product)
                if (keys.length === 0) return null

                const obj = fixed.product[keys[0]]
                if (!obj || Object.keys(obj).length === 0) return null

                const { product, productCategories: cats } = processProductData(obj, keys, colorsMap, sizesMap)
                const productVariants = processVariants(obj, keys)

                return { product, productCategories: cats, variants: productVariants, obj }
            })
        )

        results.forEach(result => {
            if (result.status === 'fulfilled' && result.value) {
                const { product, productCategories: cats, variants: vars, obj } = result.value
                products.push(product)
                productCategories.push(...cats)
                variants.push(...vars)
                const area: any[] = []
                const markingAreas: Record<string, any[]> = {}
                if (product && product.id && obj && obj.marcajes && typeof obj.marcajes === 'object') {
                    for (const [markingId, markingObj] of Object.entries(obj.marcajes)) {
                        const markingKey = `${markingId}-${product.id}`
                        for (const [fieldId, fieldObj] of Object.entries(markingObj as Record<string, any>)) {
                            const fieldData = fieldObj['1']
                            if (fieldData && typeof fieldData === 'object') {
                                const areaId = fieldId
                                const name = fieldData.name || null
                                const measures = fieldData.measures || null
                                let image = null
                                if (obj.images_marcajes && typeof obj.images_marcajes === 'object') {
                                    image = obj.images_marcajes[areaId] || null
                                }
                                if (!area.find(a => a.id === areaId)) {
                                    area.push({ id: areaId, name, measures, image, markingId })
                                }
                                if (!markingAreas[markingId]) markingAreas[markingId] = []
                                if (!markingAreas[markingId].find(a => a.id === areaId)) {
                                    markingAreas[markingId].push({ id: areaId, name, measures, image })
                                }
                            }
                        }
                        if (!markingSetGlobal.has(markingKey)) {
                            markingSetGlobal.add(markingKey)
                            marking.push({ print_id: markingId, product_id: product.id, area: markingAreas[markingId] || [] })
                        }
                    }
                }
            }
        })
    }

    const colors = Array.from(colorsMap.values())
    const sizes = Array.from(sizesMap.values())

    // 3. Save data
    await Promise.all([
        saveColors(colors),
        saveSizes(sizes),
        saveProducts(products)
    ])
    await Promise.all([
        saveProductCategory(productCategories),
        saveVariants(variants)
    ])
    await saveMarking(marking)

    return {
        product: products,
        colors,
        sizes,
        variant: variants,
        product_category: productCategories,
        marking
    }
}
