import { generateTokens } from "../../tools/auth"

interface Color {
    id: string
    name: string
    hexcode: string
}

interface Size {
    id: string
    name: string
}

interface Product {
    id: string
    active: boolean
    allow_sale_without_stock: boolean
    ean: string
    weight: number
    width: number
    depth: number
    height: number
    minimal_quantity: number
    material: string
    name: string
    description: string
    page: string
    text: string
    url_montaje: string
    image: string
    outer_box_unit: string
    inner_box_unit: string
    measure_article: string
}

export const saveColors = async (colors: Color[]) => {
    const { accessToken } = await generateTokens('system', 'system')
    const db = useDbConnector()

    const result = await db.insert(
        'enyes_color',
        colors.map(c => ({ ...c })),
        {
            constraint: 'enyes_color_pkey',
            update_columns: []
        },
        `returning {
            id
        }`,
        accessToken
    )
    return result
}

export const saveSizes = async (sizes: Size[]) => {
    const { accessToken } = await generateTokens('system', 'system')
    const db = useDbConnector()

    const result = await db.insert(
        'enyes_size',
        sizes.map(s => ({ ...s })),
        {
            constraint: 'enyes_size_pkey',
            update_columns: []
        },
        `returning {
            id
        }`,
        accessToken
    )
    return result
}

export const saveProducts = async (products: Product[]) => {
    const { accessToken } = await generateTokens('system', 'system')
    const db = useDbConnector()

    const result = await db.insert(
        'enyes_product',
        products.map(p => ({ ...p })),
        {
            constraint: 'enyes_product_pkey',
            update_columns: ['active']
        },
        `returning {
            id
        }`,
        accessToken
    )
    return result
}

export const saveProductCategory = async (product_category: any[]) => {
    const { accessToken } = await generateTokens('system', 'system')
    const db = useDbConnector()
    const categoriesResult: any = await db.get('enyes_category', {
        select: ['id']
    }, accessToken)
    const categoryList = Array.isArray(categoriesResult) ? categoriesResult : (categoriesResult.enyes_category || [])
    const validCategoryIds = new Set(categoryList.map((cat: any) => cat.id))
    const validProductCategory = product_category.filter(pc => validCategoryIds.has(pc.category_id))
    if (validProductCategory.length === 0) return { affected_rows: 0 }
    const result = await db.insert(
        'enyes_product_category',
        validProductCategory.map(pc => ({ ...pc })),
        {
            constraint: 'enyes_product_category_pkey',
            update_columns: []
        },
        `returning {
            product_id
            category_id
        }`,
        accessToken
    )
    return result
}

export const saveVariants = async (variants: any[]) => {
    const { accessToken } = await generateTokens('system', 'system')
    const db = useDbConnector()

    const result = await db.insert(
        'enyes_product_variant',
        variants.map(v => ({ ...v })),
        {
            constraint: 'enyes_product_variant_pkey',
            update_columns: ['url_360', 'ean', 'image', 'legend']
        },
        `returning {
            id
        }`,
        accessToken
    )
    return result
}

export const updatePrices = async (prices: any[]) => {
    const { accessToken } = await generateTokens('system', 'system')
    const db = useDbConnector()
    let total = 0
    for (const p of prices) {
        await db.update(
            'enyes_product_variant',
            { price_1: p.price_1 },
            { id: { _eq: p.id } },
            `returning {
                id
            }`,
            accessToken
        )
        total++
    }
    return { affected_rows: total }
}

export const updateStock = async (stockArr: { id: string, stock: number }[]) => {
    const { accessToken } = await generateTokens('system', 'system')
    const db = useDbConnector()
    let total = 0
    for (const s of stockArr) {
        if (typeof s.stock !== 'number') continue
        await db.update(
            'enyes_product_variant',
            { stock: s.stock },
            { id: { _eq: s.id } },
            `returning {
                id
            }`,
            accessToken
        )
        total++
    }
    return { affected_rows: total }
}

export const saveMarking = async (marking: any[]) => {
    const { accessToken } = await generateTokens('system', 'system')
    const db = useDbConnector()
    const printsResult: any = await db.get('enyes_print', { select: ['id'] }, accessToken)
    const printList = Array.isArray(printsResult) ? printsResult : (printsResult.enyes_print || [])
    const validPrintIds = new Set(printList.map((p: any) => p.id))
    const validMarking = marking.filter(m => validPrintIds.has(m.print_id))
    if (validMarking.length === 0) return { affected_rows: 0 }
    const result = await db.insert(
        'enyes_product_print',
        validMarking.map(m => ({
            print_id: m.print_id,
            product_id: m.product_id,
            area: Array.isArray(m.area) ? m.area : []
        })),
        {
            constraint: 'enyes_product_print_pkey',
            update_columns: ['area']
        },
        `returning {
            id
        }`,
        accessToken
    )
    return result
}

export const getProducts = async (query: any) => {
    const { accessToken } = await generateTokens('store', 'store')
    const db = useDbConnector()
    const variables = { filter: query.filter, order_by: query.order_by, limit: query.limit, offset: query.offset }
    const graphqlClient = db.getClient(accessToken)
    const q = `
        query getProducts($filter: enyes_product_bool_exp, $limit: Int, $offset: Int, $order_by: [enyes_product_order_by!]) {
            enyes_product(where: $filter, limit: $limit, offset: $offset, order_by: $order_by) {
                id
                name
                description
                ean
                slug
                image
                variants {
                    image
                    url_360
                    price_1
                }
            }
            enyes_product_aggregate(where: $filter) {
                aggregate {
                    count
                }
            }
        }
    `
    const result: any = await graphqlClient.request(q, variables)

    if (!result?.enyes_product) {
        throw new Error("store.error.productsNotFound");
    }

    return result
}

export const getMaterials = async (query: any) => {
    try {
        const { accessToken } = await generateTokens('store', 'store')
        const db = useDbConnector()

        let filterObj = query.filter
        if (typeof filterObj === 'string') {
            try {
                filterObj = JSON.parse(filterObj)
            } catch (e) {
                filterObj = {}
            }
        }

        let orderBy = query.order_by
        if (!orderBy || typeof orderBy !== 'object') {
            orderBy = [{ material: 'asc' }]
        }

        const variables = { where: filterObj, order_by: orderBy }
        const graphqlClient = db.getClient(accessToken)
        const result: any = await graphqlClient.request(`
            query GetMaterials($where: enyes_product_bool_exp!, $order_by: [enyes_product_order_by!]) {
                enyes_product(where: $where, order_by: $order_by, distinct_on: material) {
                    material
                }
            }
        `, variables)

        return Array.isArray(result?.enyes_product) ? result.enyes_product.map((m: any) => m.material).filter((mat: string | null | undefined) => !!mat) : []
    } catch (err: any) {
        console.error('Error getting materials:', err)
        throw new Error('store.error.gettingMaterials')
    }
}

export const getProduct = async (slug: string) => {
    const { accessToken } = await generateTokens('store', 'store')
    const db = useDbConnector()
    const result: any = await db.get('enyes_product', {
        select: `
            id
            name
            description
            ean
            slug
            image
            allow_sale_without_stock
            depth
            height
            width
            weight
            material
            inner_box_unit
            measure_article
            minimal_quantity
            outer_box_unit
            page
            text
            url_montaje
            categories {
                category {
                    id
                    name
                    slug
                }
            }
            variants {
                id
                ean
                image
                legend
                stock
                url_360
                color {
                    id
                    name
                    hexcode
                }
                size {
                    id
                    name
                }
                limit_1
                price_1
            }
            prints {
                id
                area
                print {
                    id
                    name
                    description
                    cliche
                    clicherep
                    minjob
                    price1
                    price2
                    price3
                    price4
                    price5
                    amountunder1
                    amountunder2
                    amountunder3
                    amountunder4
                    amountunder5
                }
            }
        `,
        where: { slug: { _eq: slug } }
    }, accessToken)

    return result?.enyes_product?.[0] || null
}

export const getColors = async () => {
    const { accessToken } = await generateTokens('store', 'store')
    const db = useDbConnector()
    const result: any = await db.get('enyes_color', {
        select: `
            id
            name
            hexcode
        `
    }, accessToken)

    return result?.enyes_color || []
}

export const getSizes = async () => {
    const { accessToken } = await generateTokens('store', 'store')
    const db = useDbConnector()
    const result: any = await db.get('enyes_size', {
        select: `
            id
            name
        `
    }, accessToken)

    return result?.enyes_size || []
}
