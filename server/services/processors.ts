import { useEnyesShop } from "~/composables/useEnyesShop";
import { generateTokens } from "../tools/auth"

const getDetails = (payload: any) => {
    const details: any[] = [];

    payload.forEach((line: any) => {
        Object.keys(line.variants).forEach((key: any) => {
            details.push({
                description: `${ line.variants[key].variant.legend } (${ line.variants[key].variant.id })`,
                quantity: line.variants[key].quantity,
                price: line.variants[key].variant.price_1
            });
        });

        const prices = useEnyesShop().calculateMarkingPrices(line.markings, line.quantity);

        line.markings.forEach((mark: any) => {
            details.push({
                description: `${ mark.marking.description } | ${ mark.marking.complement } ${ (mark.clicheRep) ? '- REPETICIÓN' : '' }`,
                quantity: (prices[mark.id].minJob) ? 1 : line.quantity,
                price: prices[mark.id].price
            });

            details.push({
                description: 'Puesta en máquina',
                quantity: 1,
                price: prices[mark.id].cliche
            });
        });
    });

    return details;
}

const paymentPaypal = async (data: any, session: any) => {
    const user = session.user.id
    const role = session.user.roles[0].role_code
    const { accessToken } = await generateTokens(user, role)
    const db = useDbConnector()
    const graphqlClient = db.getClient(accessToken)

    const query = `
        mutation savePurchasePayment($data: [sales_insert_input!]!) {
            insert_sales(objects: $data) {
                affected_rows
            }
        }
    `

    try {
        const variables = {
            data: {
                cart_id: data.cart.cartId,
                quantity_items: data.cart.quantityTotal,
                total: data.cart.summary.total,
                amount: data.cart.summary.amount,
                shipment: data.cart.summary.shipment,
                shipment_type: data.cart.summary.shipmentType,
                discount: data.cart.summary.discount,
                tax: data.cart.summary.tax,
                payload: data.cart,
                address_id: data.cart.address_id,
                details: { data: getDetails(data.cart.lines) },
                status: data.status,
                origin: 'enyes',
                payment: {
                    data: {
                        type: data.paypal?.type || 'PayPal',
                        payer_id: data.paypal.payer.payer_id,
                        payer_country: data.paypal.payer.address.country_code,
                        payer_first_name: data.paypal.payer.name.given_name,
                        payer_last_name: data.paypal.payer.name.surname,
                        payer_email: data.paypal.payer.email_address,
                        invoice_id: data.paypal.id,
                        total_amount: Number(data.paypal.purchase_units[0].payments.captures[0].amount.value),
                        net_amount: Number(data.paypal.purchase_units[0].payments.captures[0].seller_receivable_breakdown.net_amount.value),
                        paypal_fee: Number(data.paypal.purchase_units[0].payments.captures[0].seller_receivable_breakdown.paypal_fee.value),
                        currency_code: data.paypal.purchase_units[0].payments.captures[0].amount.currency_code,
                        paypal_created_at: data.paypal.purchase_units[0].payments.captures[0].create_time,
                        payload: data.paypal
                    }
                }
            }
        }

        const response = await graphqlClient.request(query, variables) as any[];
        return response;
    } catch (err: any) {
        console.error("Error saving purchase payment:", err?.response?.errors || err.message || err);
        throw err?.response?.errors || err.message || err;
    }
}

export const excecute = async (processor: string, data: any, session: any) => {
    switch (processor) {
        case 'payment-paypal':
            await paymentPaypal(data, session);
        break;
    }
}
