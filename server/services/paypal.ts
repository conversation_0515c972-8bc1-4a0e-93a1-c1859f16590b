export async function getPayPalAccessToken(clientId: string, clientSecret: string, paypalUrl: string) {
    const auth = Buffer.from(`${clientId}:${clientSecret}`).toString('base64')
    
    const response = await fetch(`${paypalUrl}/v1/oauth2/token`, {
        method: 'POST',
        headers: {
            'Authorization': `Basic ${auth}`,
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: 'grant_type=client_credentials'
    })

    if (!response.ok) {
        const errorData = await response.json()
        console.error('PayPal OAuth error:', errorData)
        throw new Error('Failed to get PayPal access token: ' + (errorData.error_description || errorData.message))
    }

    const data = await response.json()
    return data.access_token
}
