import { createError } from 'h3'
import { getPayPalAccessToken } from '../../../services/paypal'

export default defineEventHandler(async (event) => {
    const config = useRuntimeConfig()
    const paypalUrl = (config.paypal?.mode === 'sandbox') ? 'https://api.sandbox.paypal.com' : 'https://api.paypal.com'

    if (!config.paypal?.clientId || !config.paypal?.clientSecret) {
        console.error('PayPal credentials missing:', {
            clientId: !!config.public.paypal?.clientId,
            clientSecret: !!config.paypal?.clientSecret
        })
        throw createError({
            statusCode: 500,
            statusMessage: 'PayPal configuration is missing'
        })
    }

    const params = event.context.params || {}
    const query = getQuery(event)
    const orderID = params?.id || null
    const action = query.action

    let accessToken
    try {
        accessToken = await getPayPalAccessToken(config.paypal.clientId, config.paypal.clientSecret, paypalUrl)
    } catch (error: any) {
        console.error('Error getting PayPal access token:', error)
        throw createError({
            statusCode: 500,
            statusMessage: 'Failed to authenticate with PayPal',
            data: {
                error: error.message
            }
        })
    }

    if (orderID && action === 'capture') {
        try {
            const response = await fetch(`${ paypalUrl }/v2/checkout/orders/${ orderID }/capture`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json',
                    'PayPal-Request-Id': `capture_${Date.now()}_${Math.random().toString(36).substring(7)}`
                }
            })

            if (!response.ok) {
                const errorData = await response.json()
                console.error('PayPal API error:', errorData)
                throw createError({
                    statusCode: response.status,
                    statusMessage: errorData?.details[0]?.description || errorData.message || 'PayPal API error'
                })
            }

            const result = await response.json()
            return result
        } catch (error: any) {
            console.error('Error capturing PayPal order:', {
                orderID,
                name: error.name,
                message: error.message,
                details: error.details || [],
                stack: error.stack
            })

            throw createError({
                statusCode: 500,
                statusMessage: 'Failed to capture order',
                data: {
                    error: error.message,
                    details: error.details || []
                }
            })
        }
    }

    throw createError({
        statusCode: 404,
        statusMessage: 'Not found'
    })
})
