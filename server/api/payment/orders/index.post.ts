import { createError } from 'h3'
import { getPayPalAccessToken } from '../../../services/paypal'

export default defineEventHandler(async (event) => {
    const config = useRuntimeConfig()
    const paypalUrl = (config.paypal?.mode === 'sandbox') ? 'https://api.sandbox.paypal.com' : 'https://api.paypal.com'

    if (!config.paypal?.clientId || !config.paypal?.clientSecret) {
        console.error('PayPal credentials missing:', {
            clientId: !!config.public.paypal?.clientId,
            clientSecret: !!config.paypal?.clientSecret
        })
        throw createError({
            statusCode: 500,
            statusMessage: 'PayPal configuration is missing'
        })
    }

    let accessToken
    try {
        accessToken = await getPayPalAccessToken(config.paypal.clientId, config.paypal.clientSecret, paypalUrl)
    } catch (error: any) {
        console.error('Error getting PayPal access token:', error)
        throw createError({
            statusCode: 500,
            statusMessage: 'Failed to authenticate with PayPal',
            data: {
                error: error.message
            }
        })
    }

    try {
        const body = await readBody(event)
        const response = await fetch(`${ paypalUrl }/v2/checkout/orders`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json',
                'PayPal-Request-Id': `order_${Date.now()}_${Math.random().toString(36).substring(7)}`
            },
            body: JSON.stringify(body)
        })

        if (!response.ok) {
            const errorData = await response.json()
            console.error('PayPal API error:', errorData)
            throw createError({
                statusCode: response.status,
                statusMessage: errorData?.details[0]?.description || errorData.message || 'PayPal API error'
            })
        }

        const result = await response.json()
        return result
    } catch (error: any) {
        console.error('Error creating PayPal order:', {
            name: error.name,
            message: error.message,
            details: error.details || [],
            stack: error.stack
        })

        throw createError({
            statusCode: 500,
            statusMessage: 'Failed to create order',
            data: {
                error: error.message,
                details: error.details || []
            }
        })
    }
})
