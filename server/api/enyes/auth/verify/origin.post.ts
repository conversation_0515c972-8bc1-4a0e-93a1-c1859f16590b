import { verifyToken } from "~~/server/tools/utils"
import { createError } from 'h3'
import { checkUserByEmail, checkUserByPhone } from "../../../../services/system/auth"

export default defineEventHandler(async (event) => {
    await verifyToken(event);

    const { email, phone } = await readBody(event);

    if (!email && !phone) {
        throw createError({
            statusCode: 400,
            message: "error.notFound",
        });
    }

    try {
        let resp: any = null;
        if (email) {
            resp = await checkUserByEmail({ email });
        }

        if (phone) {
            resp = await checkUserByPhone({ phone });
        }

        if (!resp) {
            throw createError({
                statusCode: 400,
                message: "error.invalidCredentials",
            });
        }

        return resp;
    } catch (err: any) {
        throw createError({
            statusCode: err?.statusCode || 500,
            message: err?.message || "error.internalServerError",
        });
    }
});
