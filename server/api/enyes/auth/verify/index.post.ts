import { verifyToken } from "../../../../tools/utils"
import { createError } from 'h3'
import { verify } from "../../../../services/system/auth"

export default defineEventHandler(async (event) => {
    await verifyToken(event);

    const { id, code, origin } = await readBody(event);

    if (!id || !code || !origin) {
        throw createError({
            statusCode: 400,
            message: "error.badRequest",
        });
    }

    try {
        const resp: any = await verify({ id, code, origin });

        if (!resp.affected_rows) {
            throw createError({
                statusCode: 400,
                message: "error.verifyFailed",
            });
        }

        return resp.affected_rows;
    } catch (err: any) {
        throw createError({
            statusCode: err?.statusCode || 500,
            message: err?.message || "error.internalServerError",
        });
    }
});
