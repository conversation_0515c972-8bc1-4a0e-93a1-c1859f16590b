import { verifyToken } from "../../../../tools/utils"
import eventBus from '../../../../tools/eventBus'
import { createError, H3Event } from 'h3'
import { updatePassword } from "../../../../services/system/auth"

export default defineEventHandler(async (event: H3Event) => {
    await verifyToken(event)

    if (event.node.req.method !== 'POST') {
        throw createError({
            statusCode: 404,
            message: "error.notFound",
        })
    }

    const { email, password, code } = await readBody(event)

    if (!email || !password || !code) {
        throw createError({
            statusCode: 400,
            message: "error.badRequest"
        })
    }

    try {
        const resp = await updatePassword({ email, password, code })

        if (resp && resp?.affected_rows === 1) {
            // **** Event auth:updatePassword ****
            eventBus.emit('auth:updatePassword', { email })

            return { data: { status: 'success' } }
        } else {
            return { data: { status: 'failed' } }
        }
    } catch (err: any) {
        throw createError({
            statusCode: err?.statusCode || 500,
            message: err?.message || "error.internalServerError",
        })
    }
});
