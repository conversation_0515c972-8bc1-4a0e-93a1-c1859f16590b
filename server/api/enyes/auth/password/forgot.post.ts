import { generateRandomNumericCode, verifyToken } from "../../../../tools/utils"
import eventBus from '../../../../tools/eventBus'
import { createError } from 'h3'
import { forgotPassword } from "../../../../services/system/auth";

export default defineEventHandler(async (event) => {
    await verifyToken(event);

    const { email } = await readBody(event);

    if (!email) {
        throw createError({
            statusCode: 400,
            message: "error.badRequest"
        })
    }

    try {
        const password_code_recovery = generateRandomNumericCode(6)
        const resp = await forgotPassword({ email, password_code_recovery })

        if (resp?.affected_rows === 1) {
            // **** Event auth:forgotPassword ****
            eventBus.emit('auth:forgotPassword', { email, codeRecovery: password_code_recovery });
        }

        return { data: { status: 'success' } };
    } catch (err: any) {
        throw createError({
            statusCode: err?.statusCode || 500,
            message: err?.message || "error.internalServerError",
        });
    }
});
