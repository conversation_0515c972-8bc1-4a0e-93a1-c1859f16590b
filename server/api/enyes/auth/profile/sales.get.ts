import { verifyToken } from "~~/server/tools/utils"
import { getSales } from "~~/server/services/system/auth"

export default defineEventHandler(async (event) => {
    await verifyToken(event)

    try {
        const session: any = await getUserSession(event)

        if (session.user?.id) {
            const user = await getSales({ payload: { id: session.user?.id } , user: session.user })
            return { data: { user }}
        } else {
            console.error('User not found.')
            throw createError({
                statusCode: 404,
                message: 'store.error.userNotFound',
            })
        }
    } catch (err: any) {
        console.error('Error getting sales:', err)
        throw createError({
            statusCode: 500,
            message: 'store.error.gettingSales',
        })
    }
})
