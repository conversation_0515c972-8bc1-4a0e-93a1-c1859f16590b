import { verifyToken } from "~~/server/tools/utils";
import { insertAddress } from "~~/server/services/system/auth"

export default defineEventHandler(async (event) => {
    await verifyToken(event);

    try {
        const session: any = await getUserSession(event);
        const body = await readBody(event);

        if (session.user?.id) {
            const address = await insertAddress({ payload: { data: body }, user: session.user });
            return { data: address };
        } else {
            console.error('User not found.');
            throw createError({
                statusCode: 404,
                message: 'store.error.userNotFound',
            });
        }
    } catch (err: any) {
        console.error('Error inserting address:', err);
        throw createError({
            statusCode: 500,
            message: 'store.error.insertingAddress',
        });
    }
});
