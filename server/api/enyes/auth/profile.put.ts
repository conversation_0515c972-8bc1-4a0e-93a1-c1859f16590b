import eventBus from "~~/server/tools/eventBus"
import { verifyToken } from "~~/server/tools/utils"
import { updateProfile } from "../../../services/system/auth"


export default defineEventHandler(async (event) => {
    await verifyToken(event);
    const payload: any = await readBody(event);

    if (!payload.email || !payload.first_name || !payload.phone) {
        throw createError({
            statusCode: 400,
            message: "error.badRequest",
        });
    }

    try {
        const session: any = await getUserSession(event);
        delete payload.id;
        delete payload.username;
        delete payload.email;

        const user = await updateProfile({ payload: { id: session.user?.id, data: payload } , user: session.user });

        if (!user || !user.id) {
            throw createError({
                statusCode: 400,
                message: "error.updateProfileFailed",
            });
        }

        await replaceUserSession(event, {
            user,
            loggedInAt: new Date()
        });

        // **** Event auth:updateProfile ****
        eventBus.emit('auth:updateProfile', user);

        return { data: { status: 'success' } };
    } catch (err: any) {
        if (err[0].message === 'Uniqueness violation. duplicate key value violates unique constraint "user_phone_key"') {
            throw createError({
                statusCode: 400,
                message: "error.phoneDuplicated",
            });
        } else {
            throw createError({
                statusCode: err?.statusCode || 500,
                message: err[0]?.message || err?.message || "error.internalServerError",
            });
        }
    }
});
