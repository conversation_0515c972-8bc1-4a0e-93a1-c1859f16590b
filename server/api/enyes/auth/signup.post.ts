import { registerPayload } from "../../../interface/database.interface"
import { verifyToken, generateRandomNumericCode } from "../../../tools/utils"
import eventBus from '../../../tools/eventBus'
import { createError } from 'h3'
import { register } from "../../../services/system/auth"

export default defineEventHandler(async (event) => {
    await verifyToken(event);

    const payload: registerPayload = await readBody(event);
    if (!payload.email || !payload.password) {
        throw createError({
            statusCode: 400,
            message: "error.emailPasswordRequired",
        });
    }

    if (useRuntimeConfig().auth.register.verifyRequired) {
        const code = generateRandomNumericCode(6);
        payload.email_code_verify = code;
    }

    try {
        const user = await register({ payload, strategy: 'local' })

        if (!user || !user.id) {
            throw createError({
                statusCode: 400,
                message: "error.registrationFailed",
            });
        }

        if (!useRuntimeConfig().auth.register.verifyRequired) {
            await setUserSession(event, {
                user,
                loggedInAt: new Date()
            });
        }

        // **** Event auth:register ****
        eventBus.emit('auth:register', user)

        return { data: { status: 'success' } }
    } catch (err: any) {
        if (err.message === "error.emailDuplicated") {
            throw createError({
                statusCode: 400,
                message: "error.emailAlreadyRegistered",
            });
        }

        throw createError({
            statusCode: err?.statusCode || 500,
            message: err?.message || "error.internalServerError",
        });
    }
});
