import { verifyToken } from "../../../tools/utils"
import eventBus from '../../../tools/eventBus'
import { login } from "../../../services/system/auth"
import { createError } from 'h3'

export default defineEventHandler(async (event) => {
    await verifyToken(event);

    const { email, password } = await readBody(event)
    if (!email || !password) {
        throw createError({
            statusCode: 400,
            message: "error.emailPasswordRequired",
        });
    }

    try {
        const user: any = await login({ email, password, strategy: 'local' })

        if (useRuntimeConfig().auth.register.verifyRequired && !user.email_verify) {
            // **** Event auth:register ****
            eventBus.emit('auth:register', user);
            return { data: { status: 'failure', reason: 'error.unverifiedEmail' } };
        }

        if (!user.roles || user.roles.length === 0) {
            return { data: { status: 'failure', reason: 'error.unauthorized' } };
        }

        await setUserSession(event, {
            user,
            loggedInAt: new Date()
        });

        // **** Event auth:login ****
        eventBus.emit('auth:login', { user_id: user.id, date: new Date() });

        return { data: { status: 'success' } };
    } catch (err: any) {
        throw createError({
            statusCode: err?.statusCode || 500,
            message: err?.message || "error.internalServerError",
        });
    }
});
