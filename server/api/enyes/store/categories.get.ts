import { define<PERSON><PERSON><PERSON><PERSON><PERSON>, getQuery } from 'h3'
import { generateTokens } from "~~/server/tools/auth"

export default defineEventHandler(async (event) => {
    const { accessToken } = await generateTokens('store', 'store')
    const db = useDbConnector()
    const query = getQuery(event)
    const highlight = query.highlight === 'true' || query.highlight === true
    const where: any = {
        active: { _eq: true },
        in_menu: { _eq: true }
    }
    if (highlight) {
        where.highlight = { _eq: true }
    }
    const result: any = await db.get('enyes_category', {
        select: ['id', 'name', 'parent_id', 'slug', 'highlight', 'in_menu'],
        where
    }, accessToken)

    const categoriesFlat = Array.isArray(result) ? result : result.enyes_category || []
    const categoriesMap: Record<string, any> = {}
    categoriesFlat.forEach((cat: any) => {
        cat.subcategories = []
        categoriesMap[cat.id] = cat
    })
    const rootCategories: any[] = []
    categoriesFlat.forEach((cat: any) => {
        if (cat.parent_id && categoriesMap[cat.parent_id]) {
            categoriesMap[cat.parent_id].subcategories.push(cat)
        } else {
            rootCategories.push(cat)
        }
    })
    return {
        data: {
            categories: rootCategories
        }
    }
})
