import { getMaterials } from "../../../services/system/products"
import { createError, getQuery } from 'h3'

export default defineEventHandler(async (event) => {
    const query = getQuery(event)
    try {
        const materials = await getMaterials(query)
        return { data: { materials }}
    } catch (err: any) {
        throw createError({
            statusCode: 500,
            message: 'store.error.gettingMaterials',
        })
    }
})
