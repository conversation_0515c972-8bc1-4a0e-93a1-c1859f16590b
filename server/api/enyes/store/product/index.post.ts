import { getProducts } from "../../../../services/system/products"

export default defineEventHandler(async (event) => {
    const body = await readBody(event)
    const query: string = body?.data as any

    if (query) {
        const { enyes_product: products, enyes_product_aggregate: total } = await getProducts(query)
        return { data: { products, total }}
    } else {
        console.error('Data is mandatory.')
        throw createError({
            statusCode: 403,
            message: 'store.error.dataMandatory',
        })
    }
})
