import { getProduct } from "../../../../services/system/products"
import { createError } from 'h3'

export default defineEventHandler(async (event) => {
    const slug = event.context.params?.id as string || ''

    if (!slug) {
        console.error('Slug is mandatory.')
        throw createError({
            statusCode: 403,
            message: 'store.error.dataMandatory',
        })
    }

    const product = await getProduct(slug)
    return { data: { product }}
})
