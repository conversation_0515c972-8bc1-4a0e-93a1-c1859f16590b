import { getProducts } from "../../../../services/system/products"

export default defineEventHandler(async (event) => {
    try {
        const query: any = {
            filter: {},
            limit: 3000,
            offset: 0,
            order_by: {name: 'asc'}
        }

        const { enyes_product: products } = await getProducts(query)
        const BASE_URL = (process.env.NODE_ENV !== 'production') ? 'http://localhost:3001' : 'https://ebppublicidad.xyz'
        const urls = products.map((item: any) => `${BASE_URL}/producto/${item.slug}`)
        return urls
    } catch (err) {
        console.error('Error getting products:', err);
        throw createError({
            statusCode: 500,
            message: 'store.error.gettingProducts',
        })
    }
})
