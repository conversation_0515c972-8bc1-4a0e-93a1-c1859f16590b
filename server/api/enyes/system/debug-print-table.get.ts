import { define<PERSON><PERSON><PERSON><PERSON><PERSON> } from 'h3'
import { generateTokens } from '../../../tools/auth'

export default defineEventHandler(async (event) => {
    try {
        console.info('[debug-print-table] Starting table analysis...')

        const { accessToken } = await generateTokens('system', 'system')
        const db = useDbConnector()

        // 1. Try to get existing prints
        console.info('[debug-print-table] Fetching existing prints...')
        let existingPrints = []
        try {
            const printsResult: any = await db.get('enyes_print', { 
                select: ['id', 'name', 'description'],
                limit: 10
            }, accessToken)
            
            existingPrints = Array.isArray(printsResult) ? printsResult : (printsResult.enyes_print || [])
            console.info(`[debug-print-table] Found ${existingPrints.length} existing prints`)
        } catch (error: any) {
            console.error('[debug-print-table] Error fetching existing prints:', error.message)
        }

        // 2. Try to create a simple test print
        console.info('[debug-print-table] Attempting to create test print...')
        let createResult = null
        let createError = null
        
        try {
            const testPrint = {
                id: 'TEST_PRINT_' + Date.now(),
                name: 'Test Print',
                description: 'Test print for debugging'
            }
            
            console.info('[debug-print-table] Test print data:', JSON.stringify(testPrint, null, 2))
            
            createResult = await db.insert(
                'enyes_print',
                [testPrint],
                {
                    constraint: 'enyes_print_pkey',
                    update_columns: ['name', 'description']
                },
                `returning { id, name }`,
                accessToken
            )
            
            console.info('[debug-print-table] Create result:', JSON.stringify(createResult, null, 2))
            
        } catch (error: any) {
            createError = {
                message: error.message,
                stack: error.stack,
                type: error.constructor.name
            }
            console.error('[debug-print-table] Error creating test print:', error.message)
        }

        // 3. Try to get table schema info
        console.info('[debug-print-table] Attempting to get table schema...')
        let schemaInfo = null
        try {
            const graphqlClient = db.getClient(accessToken)
            const introspectionQuery = `
                query {
                    __type(name: "enyes_print") {
                        name
                        fields {
                            name
                            type {
                                name
                                kind
                                ofType {
                                    name
                                    kind
                                }
                            }
                        }
                    }
                }
            `
            schemaInfo = await graphqlClient.request(introspectionQuery)
        } catch (error: any) {
            console.warn('[debug-print-table] Could not get schema info:', error.message)
        }

        return {
            success: true,
            debug_info: {
                existing_prints: {
                    count: existingPrints.length,
                    data: existingPrints
                },
                create_test: {
                    success: !createError,
                    result: createResult,
                    error: createError
                },
                schema_info: schemaInfo,
                table_analysis: {
                    table_exists: existingPrints.length >= 0, // If we can query it, it exists
                    can_insert: !createError,
                    recommendations: createError ? [
                        'Check if enyes_print table exists',
                        'Verify table schema matches expected structure',
                        'Check database permissions for system user',
                        'Verify primary key constraint name'
                    ] : [
                        'Table appears to be working correctly'
                    ]
                }
            },
            timestamp: new Date().toISOString()
        }

    } catch (error: any) {
        console.error('[debug-print-table] Unexpected error:', error)
        
        return {
            success: false,
            error: {
                message: error.message,
                stack: error.stack,
                type: error.constructor.name
            },
            debug_info: {
                recommendations: [
                    'Check database connection',
                    'Verify enyes_print table exists',
                    'Check system user permissions',
                    'Review GraphQL schema configuration'
                ]
            },
            timestamp: new Date().toISOString()
        }
    }
})
