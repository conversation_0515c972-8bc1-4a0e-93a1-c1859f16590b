import { defineE<PERSON><PERSON>and<PERSON> } from 'h3'
import { productsQueue } from '../../../queues/processor'

export default defineEventHandler(async (event) => {
    const config = useRuntimeConfig()
    const { url, userId } = config.enyes

    try {
        // Add job to the products processing queue
        const job = await productsQueue.add('process-products', {
            config: {
                url,
                userId
            }
        }, {
            attempts: 3,
            removeOnComplete: 5, // Keep last 5 completed jobs
            removeOnFail: 10,    // Keep last 10 failed jobs
            backoff: {
                type: 'exponential',
                delay: 30000 // Start with 30 seconds delay
            },
            // Prevent duplicate jobs - only one products processing job at a time
            jobId: 'products-sync-' + new Date().toISOString().split('T')[0], // One job per day
            delay: 0 // Process immediately
        })

        return {
            success: true,
            message: 'Products processing job added to queue successfully',
            jobId: job.id,
            status: 'queued'
        }
    } catch (error: any) {
        console.error('Error adding products processing job to queue:', error)
        throw createError({
            statusCode: 500,
            statusMessage: 'Failed to queue products processing job',
            data: {
                error: error.message
            }
        })
    }
})
