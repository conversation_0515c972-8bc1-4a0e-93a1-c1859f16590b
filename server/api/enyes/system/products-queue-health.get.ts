import { define<PERSON><PERSON><PERSON><PERSON><PERSON> } from 'h3'
import { productsQueue } from '../../../queues/processor'

export default defineEventHandler(async (event) => {
    try {
        console.info('[products-queue-health] Checking queue health...')

        // Get queue statistics
        const waiting = await productsQueue.getWaiting()
        const active = await productsQueue.getActive()
        const completed = await productsQueue.getCompleted()
        const failed = await productsQueue.getFailed()
        const delayed = await productsQueue.getDelayed()

        // Check for stalled jobs (active jobs that might be stuck)
        const now = Date.now()
        const stalledThreshold = 15 * 60 * 1000 // 15 minutes
        
        const potentiallyStalled = active.filter(job => {
            const processedOn = job.processedOn || job.timestamp
            return (now - processedOn) > stalledThreshold
        })

        // Check Redis connection
        let redisHealth = 'unknown'
        try {
            // Try to get queue info to test Redis connection
            await productsQueue.getJobCounts()
            redisHealth = 'connected'
        } catch (error) {
            redisHealth = 'disconnected'
            console.error('[products-queue-health] Redis connection issue:', error)
        }

        // Calculate health score
        let healthScore = 100
        let issues = []

        if (failed.length > 0) {
            healthScore -= Math.min(failed.length * 10, 50)
            issues.push(`${failed.length} failed jobs`)
        }

        if (potentiallyStalled.length > 0) {
            healthScore -= potentiallyStalled.length * 30
            issues.push(`${potentiallyStalled.length} potentially stalled jobs`)
        }

        if (redisHealth === 'disconnected') {
            healthScore = 0
            issues.push('Redis disconnected')
        }

        if (waiting.length > 10) {
            healthScore -= 10
            issues.push(`${waiting.length} jobs waiting (queue backlog)`)
        }

        const healthStatus = healthScore >= 80 ? 'healthy' : 
                           healthScore >= 50 ? 'warning' : 'critical'

        // Get recent job performance
        const recentCompleted = completed.slice(-10)
        const avgProcessingTime = recentCompleted.length > 0 
            ? recentCompleted.reduce((sum, job) => {
                if (job.finishedOn && job.processedOn) {
                    return sum + (job.finishedOn - job.processedOn)
                }
                return sum
            }, 0) / recentCompleted.filter(job => job.finishedOn && job.processedOn).length
            : 0

        const result = {
            status: healthStatus,
            score: Math.max(0, healthScore),
            timestamp: new Date().toISOString(),
            
            redis: {
                status: redisHealth,
                host: useRuntimeConfig().redis.host,
                port: useRuntimeConfig().redis.port
            },
            
            queue: {
                name: 'products-processing-enyes',
                counts: {
                    waiting: waiting.length,
                    active: active.length,
                    completed: completed.length,
                    failed: failed.length,
                    delayed: delayed.length
                }
            },
            
            performance: {
                avg_processing_time_ms: Math.round(avgProcessingTime),
                avg_processing_time_formatted: avgProcessingTime > 0 
                    ? `${Math.round(avgProcessingTime / 1000)}s` 
                    : 'N/A',
                recent_jobs_analyzed: recentCompleted.length
            },
            
            issues: issues,
            
            potentially_stalled: potentiallyStalled.map(job => ({
                id: job.id,
                started: job.processedOn ? new Date(job.processedOn).toLocaleString() : 'Unknown',
                elapsed_minutes: Math.round((now - (job.processedOn || job.timestamp)) / 60000),
                progress: job.progress
            })),
            
            recommendations: []
        }

        // Add recommendations based on health
        if (failed.length > 0) {
            result.recommendations.push('Consider retrying failed jobs or investigating failure causes')
        }
        
        if (potentiallyStalled.length > 0) {
            result.recommendations.push('Check for stalled jobs and consider restarting workers')
        }
        
        if (waiting.length > 5) {
            result.recommendations.push('Consider increasing worker concurrency or adding more workers')
        }
        
        if (healthScore < 50) {
            result.recommendations.push('Queue health is critical - immediate attention required')
        }

        console.info(`[products-queue-health] Health check complete: ${healthStatus} (${healthScore}/100)`)
        
        return result

    } catch (error: any) {
        console.error('[products-queue-health] Error during health check:', error)
        
        return {
            status: 'error',
            score: 0,
            timestamp: new Date().toISOString(),
            error: {
                message: error.message,
                type: error.constructor.name
            },
            recommendations: [
                'Check server logs for detailed error information',
                'Verify Redis connection and configuration',
                'Restart the application if necessary'
            ]
        }
    }
})
