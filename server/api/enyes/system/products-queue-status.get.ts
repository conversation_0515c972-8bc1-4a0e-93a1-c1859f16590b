import { defineEvent<PERSON><PERSON><PERSON>, getQuery } from 'h3'
import { productsQueue } from '../../../queues/processor'

export default defineEventHandler(async (event) => {
    try {
        const query = getQuery(event)
        const jobId = query.jobId as string

        if (jobId) {
            // Get specific job status
            const job = await productsQueue.getJob(jobId)
            
            if (!job) {
                throw createError({
                    statusCode: 404,
                    statusMessage: 'Job not found'
                })
            }

            const state = await job.getState()
            const progress = job.progress
            const returnValue = job.returnvalue
            const failedReason = job.failedReason

            return {
                jobId: job.id,
                state,
                progress,
                data: job.data,
                result: returnValue,
                error: failedReason,
                createdAt: new Date(job.timestamp),
                processedOn: job.processedOn ? new Date(job.processedOn) : null,
                finishedOn: job.finishedOn ? new Date(job.finishedOn) : null
            }
        } else {
            // Get queue statistics
            const waiting = await productsQueue.getWaiting()
            const active = await productsQueue.getActive()
            const completed = await productsQueue.getCompleted()
            const failed = await productsQueue.getFailed()

            return {
                queue: 'products-processing-enyes',
                counts: {
                    waiting: waiting.length,
                    active: active.length,
                    completed: completed.length,
                    failed: failed.length
                },
                jobs: {
                    waiting: waiting.map(job => ({
                        id: job.id,
                        data: job.data,
                        createdAt: new Date(job.timestamp)
                    })),
                    active: active.map(job => ({
                        id: job.id,
                        data: job.data,
                        progress: job.progress,
                        createdAt: new Date(job.timestamp),
                        processedOn: job.processedOn ? new Date(job.processedOn) : null
                    })),
                    completed: completed.slice(-5).map(job => ({
                        id: job.id,
                        data: job.data,
                        result: job.returnvalue,
                        createdAt: new Date(job.timestamp),
                        processedOn: job.processedOn ? new Date(job.processedOn) : null,
                        finishedOn: job.finishedOn ? new Date(job.finishedOn) : null
                    })),
                    failed: failed.slice(-5).map(job => ({
                        id: job.id,
                        data: job.data,
                        error: job.failedReason,
                        createdAt: new Date(job.timestamp),
                        processedOn: job.processedOn ? new Date(job.processedOn) : null,
                        finishedOn: job.finishedOn ? new Date(job.finishedOn) : null
                    }))
                }
            }
        }
    } catch (error: any) {
        console.error('Error getting products queue status:', error)
        throw createError({
            statusCode: 500,
            statusMessage: 'Failed to get queue status',
            data: {
                error: error.message
            }
        })
    }
})
