import { define<PERSON>ventHandler } from 'h3'
import { updatePrices } from '../../../services/system/products'

function fixInvalidJson(text: string): any {
    const fixed = text.replace(/[\r\n\t\x00-\x1F\x7F]+/g, ' ')
    try {
        return JSON.parse(fixed)
    } catch {
        return { raw: text, error: 'No se pudo parsear' }
    }
}

export default defineEventHandler(async (event) => {
    const config = useRuntimeConfig()
    const url = config.enyes.url
    const uid = config.enyes.userId

    // 1. List of product IDs
    const catalogRes = await fetch(`${url}/w-catalogo.pro?W-USU=${uid}`)
    if (!catalogRes.ok) throw new Error('Error al consultar catálogo')
    const catalogData = await catalogRes.json()
    const ids = catalogData.products || []

    const limit = 100
    let updated = 0
    const concurrency = 10 // Cambia este valor según lo que soporte el servidor
    for (let i = 0; i < ids.length; i += limit) {
        const chunk = ids.slice(i, i + limit)
        const allPrices: { id: string, price_1: number }[] = []
        let idx = 0
        async function processNext() {
            if (idx >= chunk.length) return
            const pid = chunk[idx++]
            try {
                const res = await fetch(`${url}/w-tarifa.pro?W-USU=${uid}&w-art=${pid}`)
                if (!res.ok) return
                const text = await res.text()
                const fixed = fixInvalidJson(text)
                if (fixed && typeof fixed === 'object' && fixed.combinations) {
                    for (const [variantId, combRaw] of Object.entries(fixed.combinations)) {
                        const comb = combRaw as any
                        const price = comb.rates?.['0']?.price
                        if (typeof price === 'number') {
                            allPrices.push({ id: variantId, price_1: price })
                        }
                    }
                }
            } catch (e) {
                console.error(`Error al consultar precio para ${pid}:`, e)
            }
            await processNext()
        }
        await Promise.all(Array.from({ length: concurrency }, processNext))
        if (allPrices.length > 0) {
            const result = await updatePrices(allPrices)
            updated += result.affected_rows || 0
        }
    }
    return { updated }
})
