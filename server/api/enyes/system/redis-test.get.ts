import { define<PERSON><PERSON><PERSON><PERSON><PERSON> } from 'h3'
import { Queue } from 'bullmq'

export default defineEventHandler(async (event) => {
    const config = useRuntimeConfig()
    
    console.info('🔍 Testing Redis connection...')
    console.info('Redis config:', {
        host: config.redis?.host || 'NOT_SET',
        port: config.redis?.port || 'NOT_SET',
        password: config.redis?.password ? '***SET***' : 'NOT_SET'
    })

    try {
        // Test Redis connection by creating a temporary queue
        const testQueue = new Queue('redis-test-queue', {
            connection: {
                host: config.redis?.host || 'localhost',
                port: config.redis?.port || 6379,
                password: config.redis?.password
            }
        })

        // Try to add a test job
        const testJob = await testQueue.add('test-job', { 
            message: 'Redis connection test',
            timestamp: new Date().toISOString()
        })

        console.info('✅ Redis connection successful')
        console.info('Test job ID:', testJob.id)

        // Clean up the test job
        await testJob.remove()
        await testQueue.close()

        return {
            success: true,
            message: 'Redis connection successful',
            config: {
                host: config.redis?.host || 'localhost',
                port: config.redis?.port || 6379,
                password_set: !!config.redis?.password
            },
            test_job_id: testJob.id,
            timestamp: new Date().toISOString()
        }

    } catch (error: any) {
        console.error('❌ Redis connection failed:', error.message)
        console.error('Error details:', {
            name: error.name,
            code: error.code,
            errno: error.errno,
            syscall: error.syscall,
            address: error.address,
            port: error.port
        })

        return {
            success: false,
            message: 'Redis connection failed',
            error: {
                message: error.message,
                code: error.code,
                type: error.constructor.name
            },
            config: {
                host: config.redis?.host || 'localhost',
                port: config.redis?.port || 6379,
                password_set: !!config.redis?.password
            },
            troubleshooting: [
                'Check if Redis server is running: redis-cli ping',
                'Verify Redis host and port configuration',
                'Check if Redis password is correct (if required)',
                'Ensure Redis is accessible from the application server',
                'Check firewall settings if Redis is on a different server'
            ],
            timestamp: new Date().toISOString()
        }
    }
})
