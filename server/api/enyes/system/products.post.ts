import { define<PERSON>vent<PERSON><PERSON><PERSON>, readBody } from 'h3'
import { processProductsData } from '../../../services/system/products'
import { productsQueue } from '../../../queues/processor'



export default defineEventHandler(async (event) => {
    const config = useRuntimeConfig()
    const { url, userId } = config.enyes

    // Check if request wants to use queue or process directly
    const body = await readBody(event).catch(() => ({}))
    const useQueue = body?.useQueue !== false // Default to true

    if (useQueue) {
        // Add job to queue for background processing
        try {
            const job = await productsQueue.add('process-products', {
                config: { url, userId }
            }, {
                attempts: 3,
                removeOnComplete: 5,
                removeOnFail: 10,
                backoff: {
                    type: 'exponential',
                    delay: 30000
                },
                jobId: 'products-sync-' + new Date().toISOString().split('T')[0],
                delay: 0
            })

            return {
                success: true,
                message: 'Products processing job added to queue successfully',
                jobId: job.id,
                status: 'queued',
                useQueue: true
            }
        } catch (error: any) {
            console.error('Error adding products processing job to queue:', error)
            throw createError({
                statusCode: 500,
                statusMessage: 'Failed to queue products processing job',
                data: { error: error.message }
            })
        }
    } else {
        // Process directly (original behavior)
        try {
            const result = await processProductsData({ url, userId })
            return {
                ...result,
                success: true,
                message: 'Products processed successfully',
                useQueue: false
            }
        } catch (error: any) {
            console.error('Error processing products directly:', error)
            throw createError({
                statusCode: 500,
                statusMessage: 'Failed to process products',
                data: { error: error.message }
            })
        }
    }
})
