import { defineEvent<PERSON><PERSON><PERSON>, getQuery } from 'h3'
import { generateTokens } from '../../../tools/auth'

export default defineEventHandler(async (event) => {
    try {
        const query = getQuery(event)
        const limit = parseInt(query.limit as string) || 10

        console.info('[debug-markings] Starting debug analysis...')

        const { accessToken } = await generateTokens('system', 'system')
        const db = useDbConnector()

        // 1. Check enyes_print table
        console.info('[debug-markings] Checking enyes_print table...')
        const printsResult: any = await db.get('enyes_print', { 
            select: ['id', 'name', 'description'],
            limit: limit
        }, accessToken)
        
        const printList = Array.isArray(printsResult) ? printsResult : (printsResult.enyes_print || [])
        console.info(`[debug-markings] Found ${printList.length} prints`)

        // 2. Check enyes_product_print table
        console.info('[debug-markings] Checking enyes_product_print table...')
        const productPrintsResult: any = await db.get('enyes_product_print', { 
            select: ['id', 'print_id', 'product_id', 'area'],
            limit: limit
        }, accessToken)
        
        const productPrintList = Array.isArray(productPrintsResult) ? productPrintsResult : (productPrintsResult.enyes_product_print || [])
        console.info(`[debug-markings] Found ${productPrintList.length} product-print relationships`)

        // 3. Check table schema/constraints
        console.info('[debug-markings] Checking table constraints...')
        
        // Try to get table info using GraphQL introspection
        const graphqlClient = db.getClient(accessToken)
        let schemaInfo = null
        
        try {
            const introspectionQuery = `
                query {
                    __type(name: "enyes_product_print") {
                        name
                        fields {
                            name
                            type {
                                name
                                kind
                            }
                        }
                    }
                }
            `
            schemaInfo = await graphqlClient.request(introspectionQuery)
        } catch (error: any) {
            console.warn('[debug-markings] Could not get schema info:', error.message)
        }

        // 4. Sample data analysis
        const sampleProductPrint = productPrintList[0]
        let areaAnalysis = null
        
        if (sampleProductPrint?.area) {
            areaAnalysis = {
                type: typeof sampleProductPrint.area,
                isArray: Array.isArray(sampleProductPrint.area),
                length: Array.isArray(sampleProductPrint.area) ? sampleProductPrint.area.length : 'N/A',
                sample: Array.isArray(sampleProductPrint.area) ? sampleProductPrint.area.slice(0, 2) : sampleProductPrint.area
            }
        }

        return {
            success: true,
            debug_info: {
                prints: {
                    count: printList.length,
                    sample: printList.slice(0, 3),
                    all_ids: printList.map(p => p.id)
                },
                product_prints: {
                    count: productPrintList.length,
                    sample: productPrintList.slice(0, 3)
                },
                schema_info: schemaInfo,
                area_analysis: areaAnalysis,
                recommendations: [
                    'Check if enyes_print table has the required print IDs',
                    'Verify that area field accepts JSON/JSONB data type',
                    'Ensure no constraint violations on composite keys',
                    'Check if area data structure matches expected schema'
                ]
            },
            timestamp: new Date().toISOString()
        }

    } catch (error: any) {
        console.error('[debug-markings] Error during debug analysis:', error)
        
        return {
            success: false,
            error: {
                message: error.message,
                stack: error.stack,
                type: error.constructor.name
            },
            debug_info: {
                error_analysis: [
                    'Check database connection',
                    'Verify table exists: enyes_print, enyes_product_print',
                    'Check access permissions for system user',
                    'Verify GraphQL schema is up to date'
                ]
            },
            timestamp: new Date().toISOString()
        }
    }
})
