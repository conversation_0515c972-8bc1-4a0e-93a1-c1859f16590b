import { defineEventHandler, readBody } from 'h3'
import { productsQueue } from '../../../queues/processor'

export default defineEventHandler(async (event) => {
    try {
        const body = await readBody(event).catch(() => ({}))
        const action = body.action || 'clean'

        console.info(`[products-queue-cleanup] Starting cleanup action: ${action}`)

        let result: any = {}

        switch (action) {
            case 'clean':
                // Clean completed and failed jobs
                const cleanedCompleted = await productsQueue.clean(0, 100, 'completed')
                const cleanedFailed = await productsQueue.clean(0, 100, 'failed')
                
                result = {
                    action: 'clean',
                    cleaned: {
                        completed: cleanedCompleted,
                        failed: cleanedFailed
                    }
                }
                console.info(`[products-queue-cleanup] Cleaned ${cleanedCompleted} completed and ${cleanedFailed} failed jobs`)
                break

            case 'drain':
                // Remove all jobs from the queue
                await productsQueue.drain()
                result = {
                    action: 'drain',
                    message: 'All jobs removed from queue'
                }
                console.info('[products-queue-cleanup] Drained all jobs from queue')
                break

            case 'obliterate':
                // Completely obliterate the queue
                await productsQueue.obliterate()
                result = {
                    action: 'obliterate',
                    message: 'Queue completely obliterated and recreated'
                }
                console.info('[products-queue-cleanup] Queue obliterated')
                break

            case 'retry-failed':
                // Retry all failed jobs
                const failedJobs = await productsQueue.getFailed()
                let retriedCount = 0
                
                for (const job of failedJobs) {
                    try {
                        await job.retry()
                        retriedCount++
                    } catch (error) {
                        console.warn(`[products-queue-cleanup] Could not retry job ${job.id}:`, error)
                    }
                }
                
                result = {
                    action: 'retry-failed',
                    retried: retriedCount,
                    total_failed: failedJobs.length
                }
                console.info(`[products-queue-cleanup] Retried ${retriedCount} of ${failedJobs.length} failed jobs`)
                break

            case 'status':
                // Get detailed status
                const waiting = await productsQueue.getWaiting()
                const active = await productsQueue.getActive()
                const completed = await productsQueue.getCompleted()
                const failed = await productsQueue.getFailed()
                const delayed = await productsQueue.getDelayed()

                result = {
                    action: 'status',
                    counts: {
                        waiting: waiting.length,
                        active: active.length,
                        completed: completed.length,
                        failed: failed.length,
                        delayed: delayed.length
                    },
                    active_jobs: active.map(job => ({
                        id: job.id,
                        progress: job.progress,
                        timestamp: new Date(job.timestamp).toLocaleString(),
                        processedOn: job.processedOn ? new Date(job.processedOn).toLocaleString() : null
                    })),
                    failed_jobs: failed.slice(-5).map(job => ({
                        id: job.id,
                        failedReason: job.failedReason,
                        timestamp: new Date(job.timestamp).toLocaleString(),
                        finishedOn: job.finishedOn ? new Date(job.finishedOn).toLocaleString() : null
                    }))
                }
                break

            default:
                throw createError({
                    statusCode: 400,
                    statusMessage: `Unknown action: ${action}. Valid actions: clean, drain, obliterate, retry-failed, status`
                })
        }

        return {
            success: true,
            timestamp: new Date().toISOString(),
            ...result
        }

    } catch (error: any) {
        console.error('[products-queue-cleanup] Error during cleanup:', error)
        
        throw createError({
            statusCode: 500,
            statusMessage: 'Failed to cleanup queue',
            data: {
                error: error.message,
                type: error.constructor.name
            }
        })
    }
})
