import { defineEvent<PERSON><PERSON><PERSON>, readBody } from 'h3'
import { saveMarking } from '../../../services/system/products'

export default defineEventHandler(async (event) => {
    try {
        const body = await readBody(event).catch(() => ({}))
        const testMode = body.testMode !== false

        console.info('[test-markings] Starting marking test...')

        if (testMode) {
            // Create test data
            const testMarkings = [
                {
                    print_id: 'test-print-1',
                    product_id: 'test-product-1',
                    area: [
                        {
                            id: 'area-1',
                            name: 'Front',
                            measures: '10x5cm',
                            image: null
                        }
                    ]
                },
                {
                    print_id: 'test-print-2',
                    product_id: 'test-product-2',
                    area: [
                        {
                            id: 'area-2',
                            name: 'Back',
                            measures: '8x3cm',
                            image: 'https://example.com/image.jpg'
                        },
                        {
                            id: 'area-3',
                            name: 'Side',
                            measures: '5x2cm',
                            image: null
                        }
                    ]
                }
            ]

            console.info('[test-markings] Using test data:', JSON.stringify(testMarkings, null, 2))

            try {
                const result = await saveMarking(testMarkings)
                
                return {
                    success: true,
                    message: 'Test markings processed successfully',
                    test_mode: true,
                    input_data: testMarkings,
                    result: result,
                    timestamp: new Date().toISOString()
                }
            } catch (error: any) {
                console.error('[test-markings] Test failed:', error)
                
                return {
                    success: false,
                    message: 'Test markings failed',
                    test_mode: true,
                    input_data: testMarkings,
                    error: {
                        message: error.message,
                        stack: error.stack,
                        type: error.constructor.name
                    },
                    timestamp: new Date().toISOString()
                }
            }
        } else {
            // Use provided data
            const markings = body.markings || []
            
            if (!Array.isArray(markings) || markings.length === 0) {
                throw createError({
                    statusCode: 400,
                    statusMessage: 'markings array is required when testMode is false'
                })
            }

            console.info(`[test-markings] Processing ${markings.length} provided markings`)

            try {
                const result = await saveMarking(markings)
                
                return {
                    success: true,
                    message: 'Markings processed successfully',
                    test_mode: false,
                    input_count: markings.length,
                    result: result,
                    timestamp: new Date().toISOString()
                }
            } catch (error: any) {
                console.error('[test-markings] Processing failed:', error)
                
                return {
                    success: false,
                    message: 'Markings processing failed',
                    test_mode: false,
                    input_count: markings.length,
                    error: {
                        message: error.message,
                        stack: error.stack,
                        type: error.constructor.name
                    },
                    timestamp: new Date().toISOString()
                }
            }
        }

    } catch (error: any) {
        console.error('[test-markings] Unexpected error:', error)
        
        throw createError({
            statusCode: 500,
            statusMessage: 'Failed to test markings',
            data: {
                error: error.message,
                type: error.constructor.name
            }
        })
    }
})
