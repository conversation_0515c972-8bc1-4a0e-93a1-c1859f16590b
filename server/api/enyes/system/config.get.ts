import { generateTokens } from "../../../tools/auth";

export default defineEventHandler(async (event) => {
    try {
        const { accessToken } = await generateTokens('system', 'system');
        const db = useDbConnector()
        const config = await db.get('enyes_config', {
            select: [
                'top_bar_message',
                'phone_contact',
                'email_contact',
                'carousel_category',
                'social_net',
                'tax',
                'notify',
                'shipping',
                'shipping_free',
                'discount',
                'img_1',
                'img_2',
                'img_3',
                'img_4',
                'link_1',
                'link_2',
                'link_3',
                'link_4',
                'text_1',
                'text_2',
                'text_3',
                'text_4',
                'increase'
            ]
        }, accessToken);
        return { data: { config }};
    } catch (err: any) {
        throw createError({
            statusCode: 500,
            statusMessage: err.message
        })
    }
})
