import { define<PERSON><PERSON><PERSON><PERSON><PERSON> } from 'h3'
import { productsQueue } from '../../../queues/processor'

export default defineEventHandler(async (event) => {
    try {
        console.info('\n🧪 #### SIMPLE QUEUE TEST ####')
        console.info('📋 Testing basic queue functionality...')
        
        // Add a simple test job
        const job = await productsQueue.add('test-simple', {
            message: 'Simple test job',
            timestamp: new Date().toISOString()
        }, {
            attempts: 1,
            removeOnComplete: 1,
            removeOnFail: 1,
            jobId: 'simple-test-' + Date.now()
        })

        console.info(`✅ Test job added successfully!`)
        console.info(`📋 Job ID: ${job.id}`)
        console.info(`⏰ Added at: ${new Date().toLocaleString()}`)
        console.info('=====================================\n')

        return {
            success: true,
            message: 'Simple test job added to queue successfully',
            jobId: job.id,
            status: 'queued',
            timestamp: new Date().toISOString()
        }

    } catch (error: any) {
        console.error('\n❌ #### SIMPLE QUEUE TEST FAILED ####')
        console.error(`💥 Error: ${error.message}`)
        console.error(`📍 Stack: ${error.stack}`)
        console.error('====================================\n')

        throw createError({
            statusCode: 500,
            statusMessage: 'Failed to add simple test job to queue',
            data: {
                error: error.message,
                type: error.constructor.name
            }
        })
    }
})
