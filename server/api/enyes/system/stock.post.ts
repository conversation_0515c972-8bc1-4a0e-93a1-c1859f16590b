import { define<PERSON>ventHandler } from 'h3'
import { updateStock } from '../../../services/system/products'

function fixInvalidJson(text: string): any {
    const fixed = text.replace(/[\r\n\t\x00-\x1F\x7F]+/g, ' ')
    try {
        return JSON.parse(fixed)
    } catch {
        return { raw: text, error: 'No se pudo parsear' }
    }
}

export default defineEventHandler(async (event) => {
    const config = useRuntimeConfig()
    const url = config.enyes.url
    const uid = config.enyes.userId

    // 1. Obtener lista de productos
    const catalogRes = await fetch(`${url}/w-catalogo.pro?W-USU=${uid}`)
    if (!catalogRes.ok) throw new Error('Error al consultar catálogo')
    const catalogData = await catalogRes.json()
    const ids = catalogData.products || []

    const limit = 50
    const concurrency = 10 // Ajustar este valor según lo que soporte el servidor
    let updated = 0
    for (let i = 0; i < ids.length; i += limit) {
        const chunk = ids.slice(i, i + limit)
        const allStock: { id: string, stock: number }[] = []
        let idx = 0
        async function processNext() {
            if (idx >= chunk.length) return
            const pid = chunk[idx++]
            try {
                const res = await fetch(`${url}/w-stock.pro?W-USU=${uid}&w-art=${pid}`)
                if (!res.ok) return
                const text = await res.text()
                const fixed = fixInvalidJson(text)
                if (fixed && typeof fixed === 'object' && fixed.combinations) {
                    for (const [variantId, combRaw] of Object.entries(fixed.combinations)) {
                        const comb = combRaw as any
                        const quantity = comb.quantity
                        if (typeof quantity === 'number') {
                            allStock.push({ id: variantId, stock: quantity })
                        }
                    }
                }
            } catch (e) {
                console.error(`Error al consultar stock para ${pid}:`, e)
            }
            await processNext()
        }
        await Promise.all(Array.from({ length: concurrency }, processNext))
        if (allStock.length > 0) {
            const result = await updateStock(allStock)
            updated += result.affected_rows || 0
        }
    }
    return { updated }
})
