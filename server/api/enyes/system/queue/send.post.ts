import { allProcessorsQueue } from '../../../../queues/processor';

export default defineEventHandler(async (event) => {
    // **** body = { processor, data } ****
    const body = await readBody(event);
    const session: any = await getUserSession(event);
    body.session = session;

    try {
        await allProcessorsQueue.add('all-processors-enyes', body, {
            attempts: 3, 
            removeOnComplete: true,
            removeOnFail: 1000,
            backoff: {
                type: 'exponential',
                delay: 60000
            }
        });
        return 'Work added to queue all-processors-enyes.';
    } catch (err: any) {
        console.error('Error adding process to queue all-processors-enyes:', err);
        throw createError({
            statusCode: 500,
            message: 'queue.error.addingProcessToQueue',
        });
    }
});
