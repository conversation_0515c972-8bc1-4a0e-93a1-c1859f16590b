import { define<PERSON><PERSON><PERSON>and<PERSON> } from 'h3'
import { productsQueue } from '../../../queues/processor'

export default defineEventHandler(async (event) => {
    const config = useRuntimeConfig()
    const { url, userId } = config.enyes

    try {
        console.info('\n🧪 #### TESTING PRODUCTS QUEUE ####')
        console.info('📋 Adding test job to products queue...')
        console.info('⚙️  Configuration:')
        console.info(`   - URL: ${url}`)
        console.info(`   - User ID: ${userId}`)
        console.info('=====================================\n')

        // Add job to the products processing queue
        const job = await productsQueue.add('process-products', {
            config: {
                url,
                userId
            }
        }, {
            attempts: 3,
            removeOnComplete: 5, // Keep last 5 completed jobs
            removeOnFail: 10,    // Keep last 10 failed jobs
            backoff: {
                type: 'exponential',
                delay: 30000 // Start with 30 seconds delay
            },
            // Unique job ID for testing
            jobId: 'test-products-sync-' + Date.now(),
            delay: 0 // Process immediately
        })

        console.info('✅ Test job added successfully!')
        console.info(`📋 Job ID: ${job.id}`)
        console.info(`⏰ Added at: ${new Date().toLocaleString()}`)
        console.info('=====================================\n')

        return {
            success: true,
            message: 'Test products processing job added to queue successfully',
            jobId: job.id,
            status: 'queued',
            testMode: true,
            timestamp: new Date().toISOString(),
            config: {
                url: url ? '***configured***' : 'not configured',
                userId: userId ? '***configured***' : 'not configured'
            }
        }
    } catch (error: any) {
        console.error('\n❌ #### TEST FAILED ####')
        console.error(`💥 Error: ${error.message}`)
        console.error(`📍 Stack: ${error.stack}`)
        console.error('========================\n')

        throw createError({
            statusCode: 500,
            statusMessage: 'Failed to queue test products processing job',
            data: {
                error: error.message,
                testMode: true
            }
        })
    }
})
