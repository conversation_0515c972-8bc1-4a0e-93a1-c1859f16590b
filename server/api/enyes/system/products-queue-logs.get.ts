import { defineEvent<PERSON><PERSON><PERSON>, getQuery } from 'h3'
import { productsQueue } from '../../../queues/processor'

export default defineEventHandler(async (event) => {
    try {
        const query = getQuery(event)
        const jobId = query.jobId as string
        const format = query.format as string || 'json'

        if (!jobId) {
            throw createError({
                statusCode: 400,
                statusMessage: 'jobId parameter is required'
            })
        }

        // Get specific job
        const job = await productsQueue.getJob(jobId)
        
        if (!job) {
            throw createError({
                statusCode: 404,
                statusMessage: 'Job not found'
            })
        }

        const state = await job.getState()
        const progress = job.progress
        const logs = job.logs || []

        // Format progress information
        let progressInfo = 'No progress information'
        if (progress && typeof progress === 'object') {
            const p = progress as any
            if (p.percentage !== undefined && p.message) {
                const elapsed = p.elapsed ? Math.floor(p.elapsed / 1000) : 0
                progressInfo = `${p.percentage}% - ${p.message} (${elapsed}s elapsed)`
            }
        } else if (typeof progress === 'number') {
            progressInfo = `${progress}%`
        }

        const jobInfo = {
            jobId: job.id,
            state,
            progress: progressInfo,
            createdAt: new Date(job.timestamp).toLocaleString(),
            processedOn: job.processedOn ? new Date(job.processedOn).toLocaleString() : null,
            finishedOn: job.finishedOn ? new Date(job.finishedOn).toLocaleString() : null,
            logs: logs.slice(-20), // Last 20 log entries
            data: job.data,
            result: job.returnvalue,
            error: job.failedReason
        }

        if (format === 'text') {
            // Return formatted text for console display
            let output = `
🔍 #### JOB STATUS REPORT ####
📋 Job ID: ${jobInfo.jobId}
📊 State: ${jobInfo.state}
📈 Progress: ${jobInfo.progress}
⏰ Created: ${jobInfo.createdAt}
${jobInfo.processedOn ? `🚀 Started: ${jobInfo.processedOn}` : ''}
${jobInfo.finishedOn ? `✅ Finished: ${jobInfo.finishedOn}` : ''}

📝 Recent Logs:
${logs.length > 0 ? logs.slice(-10).map((log: string, index: number) => `   ${index + 1}. ${log}`).join('\n') : '   No logs available'}

${jobInfo.error ? `❌ Error: ${jobInfo.error}` : ''}
${jobInfo.result ? `📊 Results: ${JSON.stringify(jobInfo.result, null, 2)}` : ''}
=====================================
`
            return output
        }

        return jobInfo
    } catch (error: any) {
        console.error('Error getting job logs:', error)
        throw createError({
            statusCode: 500,
            statusMessage: 'Failed to get job logs',
            data: {
                error: error.message
            }
        })
    }
})
