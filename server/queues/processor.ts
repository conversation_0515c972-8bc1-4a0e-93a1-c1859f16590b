import { getError } from "../tools/errors";
import { Queue, Worker } from 'bullmq';
// import {
//     processCategory,
//     processMasterData,
//     processPriceList,
//     processStock
// } from '../roly';
import { excecute } from '../services/processors';
import { processProductsData } from '../services/system/products';

// export const dataProcessingQueue = new Queue('data-processing-enyes', {
//     connection: {
//         host: useRuntimeConfig().redis.host,
//         port: useRuntimeConfig().redis.port,
//         password: useRuntimeConfig().redis.password
//     }
// });

// const worker = new Worker('data-processing-enyes', async (job) => {
//     const { origin } = job.data as { origin: string };

//     try {
//         console.info(`#### Worker data-processing-enyes for ${origin} started. ####`);
//         switch (origin) {
//             case 'category':
//                 await processCategory();
//             break;
//             case 'master':
//                 await processMasterData();
//             break;
//             case 'price-list':
//                 await processPriceList();
//             break;
//             case 'stock':
//                 await processStock();
//             break;
//         }
//         console.info(`#### Worker data-processing-enyes for ${origin} finished. ####`);
//     } catch (err: any) {
//         console.error(`Error en el job ${job.id}, process ${origin}:`, err);
//         throw getError(err);
//     }
// }, {
//     connection: {
//         host: useRuntimeConfig().redis.host,
//         port: useRuntimeConfig().redis.port,
//         password: useRuntimeConfig().redis.password
//     },
//     lockDuration: 30000
// });

// worker.on('completed', (job: any) => {
//     console.info(`Job ${job.id} completed, process ${job.data.origin}.`);
// });

// worker.on('failed', (job: any, err: any) => {
//     console.error(`Job ${job.id} failed, process ${job.data.origin}: ${err?.message}`);
//     throw err;
// });

export const allProcessorsQueue = new Queue('all-processors-enyes', {
    connection: {
        host: useRuntimeConfig().redis.host,
        port: useRuntimeConfig().redis.port,
        password: useRuntimeConfig().redis.password
    }
});

const workerAllProcesor = new Worker('all-processors-enyes', async (job) => {
    const { processor, data, session } = job.data;
    await excecute(processor, data, session);
}, {
    connection: {
        host: useRuntimeConfig().redis.host,
        port: useRuntimeConfig().redis.port,
        password: useRuntimeConfig().redis.password
    },
    lockDuration: 30000
});

workerAllProcesor.on('completed', (job: any) => {
    console.info(`Job ${job.id} completed.`);
});

workerAllProcesor.on('failed', async (job: any, err: any) => {
    console.error(`Job ${ job.id } failed: ${ err?.message } - ${ JSON.stringify(job.data) }`);
});

// Products processing queue
export const productsQueue = new Queue('products-processing-enyes', {
    connection: {
        host: useRuntimeConfig().redis.host,
        port: useRuntimeConfig().redis.port,
        password: useRuntimeConfig().redis.password
    }
});

const productsWorker = new Worker('products-processing-enyes', async (job) => {
    const { config } = job.data;
    console.info(`#### Worker products-processing-enyes started. Job ID: ${job.id} ####`);

    try {
        // Update job progress
        await job.updateProgress(10);

        const result = await processProductsData(config);

        // Update job progress to completion
        await job.updateProgress(100);

        console.info(`#### Worker products-processing-enyes finished. Job ID: ${job.id} ####`);
        return result;
    } catch (error: any) {
        console.error(`Error in products processing job ${job.id}:`, error);
        throw error;
    }
}, {
    connection: {
        host: useRuntimeConfig().redis.host,
        port: useRuntimeConfig().redis.port,
        password: useRuntimeConfig().redis.password
    },
    lockDuration: 300000, // 5 minutes - longer for product processing
    concurrency: 1 // Process one at a time to avoid overwhelming the external API
});

productsWorker.on('completed', (job: any, result: any) => {
    console.info(`Products processing job ${job.id} completed successfully.`);
    console.info(`Processed: ${result?.product?.length || 0} products, ${result?.colors?.length || 0} colors, ${result?.sizes?.length || 0} sizes`);
});

productsWorker.on('failed', (job: any, err: any) => {
    console.error(`Products processing job ${job.id} failed: ${err?.message}`);
});

productsWorker.on('progress', (job: any, progress: any) => {
    console.info(`Products processing job ${job.id} progress: ${JSON.stringify(progress)}`);
});
