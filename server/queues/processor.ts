import { getError } from "../tools/errors";
import { Queue, Worker } from 'bullmq';
// import {
//     processCategory,
//     processMasterData,
//     processPriceList,
//     processStock
// } from '../roly';
import { excecute } from '../services/processors';
import { processProductsData } from '../services/system/products';

// export const dataProcessingQueue = new Queue('data-processing-enyes', {
//     connection: {
//         host: useRuntimeConfig().redis.host,
//         port: useRuntimeConfig().redis.port,
//         password: useRuntimeConfig().redis.password
//     }
// });

// const worker = new Worker('data-processing-enyes', async (job) => {
//     const { origin } = job.data as { origin: string };

//     try {
//         console.info(`#### Worker data-processing-enyes for ${origin} started. ####`);
//         switch (origin) {
//             case 'category':
//                 await processCategory();
//             break;
//             case 'master':
//                 await processMasterData();
//             break;
//             case 'price-list':
//                 await processPriceList();
//             break;
//             case 'stock':
//                 await processStock();
//             break;
//         }
//         console.info(`#### Worker data-processing-enyes for ${origin} finished. ####`);
//     } catch (err: any) {
//         console.error(`Error en el job ${job.id}, process ${origin}:`, err);
//         throw getError(err);
//     }
// }, {
//     connection: {
//         host: useRuntimeConfig().redis.host,
//         port: useRuntimeConfig().redis.port,
//         password: useRuntimeConfig().redis.password
//     },
//     lockDuration: 30000
// });

// worker.on('completed', (job: any) => {
//     console.info(`Job ${job.id} completed, process ${job.data.origin}.`);
// });

// worker.on('failed', (job: any, err: any) => {
//     console.error(`Job ${job.id} failed, process ${job.data.origin}: ${err?.message}`);
//     throw err;
// });

export const allProcessorsQueue = new Queue('all-processors-enyes', {
    connection: {
        host: useRuntimeConfig().redis.host,
        port: useRuntimeConfig().redis.port,
        password: useRuntimeConfig().redis.password
    }
});

const workerAllProcesor = new Worker('all-processors-enyes', async (job) => {
    const { processor, data, session } = job.data;
    await excecute(processor, data, session);
}, {
    connection: {
        host: useRuntimeConfig().redis.host,
        port: useRuntimeConfig().redis.port,
        password: useRuntimeConfig().redis.password
    },
    lockDuration: 30000
});

workerAllProcesor.on('completed', (job: any) => {
    console.info(`Job ${job.id} completed.`);
});

workerAllProcesor.on('failed', async (job: any, err: any) => {
    console.error(`Job ${ job.id } failed: ${ err?.message } - ${ JSON.stringify(job.data) }`);
});

// Products processing queue
export const productsQueue = new Queue('products-processing-enyes', {
    connection: {
        host: useRuntimeConfig().redis.host,
        port: useRuntimeConfig().redis.port,
        password: useRuntimeConfig().redis.password
    }
});

const productsWorker = new Worker('products-processing-enyes', async (job) => {
    const { config } = job.data;
    const startTime = Date.now();

    console.info(`\n🚀 #### PRODUCTS WORKER STARTED ####`);
    console.info(`📋 Job ID: ${job.id}`);
    console.info(`⏰ Started at: ${new Date().toLocaleString()}`);
    console.info(`🔧 Config: ${JSON.stringify(config, null, 2)}`);
    console.info(`==========================================\n`);

    try {
        // Progress callback to update job progress and log detailed info
        const progressCallback = async (progress: number, message: string) => {
            await job.updateProgress({
                percentage: progress,
                message,
                timestamp: new Date().toISOString(),
                elapsed: Date.now() - startTime
            });

            const elapsed = Math.floor((Date.now() - startTime) / 1000);
            console.info(`📊 [${progress.toString().padStart(3)}%] ${message} (${elapsed}s elapsed)`);
        };

        const result = await processProductsData(config, progressCallback);

        const totalTime = Math.floor((Date.now() - startTime) / 1000);

        console.info(`\n✅ #### PRODUCTS WORKER COMPLETED ####`);
        console.info(`📋 Job ID: ${job.id}`);
        console.info(`⏱️  Total time: ${totalTime}s`);
        console.info(`📊 Results:`);
        console.info(`   - Products: ${result.product?.length || 0}`);
        console.info(`   - Colors: ${result.colors?.length || 0}`);
        console.info(`   - Sizes: ${result.sizes?.length || 0}`);
        console.info(`   - Variants: ${result.variant?.length || 0}`);
        console.info(`   - Categories: ${result.product_category?.length || 0}`);
        console.info(`   - Markings: ${result.marking?.affected_rows || 0} (${result.marking?.failed ? 'with errors' : 'success'})`);
        console.info(`⏰ Finished at: ${new Date().toLocaleString()}`);
        console.info(`==========================================\n`);

        return result;
    } catch (error: any) {
        const totalTime = Math.floor((Date.now() - startTime) / 1000);

        console.error(`\n❌ #### PRODUCTS WORKER FAILED ####`);
        console.error(`📋 Job ID: ${job.id}`);
        console.error(`⏱️  Failed after: ${totalTime}s`);
        console.error(`💥 Error: ${error.message}`);
        console.error(`📍 Stack: ${error.stack}`);
        console.error(`⏰ Failed at: ${new Date().toLocaleString()}`);
        console.error(`==========================================\n`);

        throw error;
    }
}, {
    connection: {
        host: useRuntimeConfig().redis.host,
        port: useRuntimeConfig().redis.port,
        password: useRuntimeConfig().redis.password
    },
    lockDuration: 300000, // 5 minutes - longer for product processing
    concurrency: 1 // Process one at a time to avoid overwhelming the external API
});

productsWorker.on('completed', (job: any, result: any) => {
    console.info(`\n🎉 #### JOB COMPLETED EVENT ####`);
    console.info(`📋 Job ID: ${job.id}`);
    console.info(`📊 Final Results Summary:`);
    console.info(`   ✓ Products: ${result?.product?.length || 0}`);
    console.info(`   ✓ Colors: ${result?.colors?.length || 0}`);
    console.info(`   ✓ Sizes: ${result?.sizes?.length || 0}`);
    console.info(`   ✓ Variants: ${result?.variant?.length || 0}`);
    console.info(`   ✓ Categories: ${result?.product_category?.length || 0}`);
    console.info(`   ✓ Markings: ${result?.marking?.affected_rows || 0} ${result?.marking?.failed ? '(with errors)' : ''}`);
    console.info(`⏰ Completed at: ${new Date().toLocaleString()}`);
    console.info(`=====================================\n`);
});

productsWorker.on('failed', (job: any, err: any) => {
    console.error(`\n💥 #### JOB FAILED EVENT ####`);
    console.error(`📋 Job ID: ${job.id}`);
    console.error(`❌ Error: ${err?.message}`);
    console.error(`📍 Stack: ${err?.stack}`);
    console.error(`⏰ Failed at: ${new Date().toLocaleString()}`);
    console.error(`==================================\n`);
});

productsWorker.on('progress', (job: any, progress: any) => {
    // Only log progress if it has detailed information
    if (progress && typeof progress === 'object' && progress.message) {
        const elapsed = progress.elapsed ? Math.floor(progress.elapsed / 1000) : 0;
        console.info(`🔄 [Job ${job.id}] ${progress.percentage}% - ${progress.message} (${elapsed}s)`);
    }
});

// Additional worker events for better monitoring
productsWorker.on('active', (job: any) => {
    console.info(`\n🔥 #### JOB STARTED ####`);
    console.info(`📋 Job ID: ${job.id}`);
    console.info(`⏰ Started at: ${new Date().toLocaleString()}`);
    console.info(`========================\n`);
});

productsWorker.on('stalled', (jobId: string) => {
    console.warn(`\n⚠️  #### JOB STALLED ####`);
    console.warn(`📋 Job ID: ${jobId}`);
    console.warn(`⏰ Stalled at: ${new Date().toLocaleString()}`);
    console.warn(`=========================\n`);
});
