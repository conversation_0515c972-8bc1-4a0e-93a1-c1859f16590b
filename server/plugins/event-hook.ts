import eventBus from '../tools/eventBus'
import { MailOptions } from "../interface/email.interface"

export default defineNitroPlugin(async (nitroApp) => {
    eventBus.on('auth:register', (data) => {
        if (useRuntimeConfig().auth.register.verifyRequired) {
            const mailOptions: MailOptions = {
                subject: '¡Tu código de verificación para EBP Publicidad!',
                to: data.email,
                template: 'send_code',
                context: { code: data.email_code_verify }
            }
            const sender = useMailSender()

            sender.send(mailOptions);
        }

        console.info('Captured event auth:register:', data);
    });

    eventBus.on('auth:updateProfile', (data) => {
        console.info('Captured event auth:updateProfile:', data);
    });

    eventBus.on('auth:login', (data) => {
        console.info('Captured event auth:login:', data);
    });

    eventBus.on('auth:forgotPassword', (data) => {
        if (data.email && data.codeRecovery) {
            const mailOptions: MailOptions = {
                subject: '¡Tu código de verificación para EBP Publicidad!',
                to: data.email,
                template: 'send_code_forgot',
                context: { code: data.codeRecovery }
            }
            const sender = useMailSender()

            sender.send(mailOptions);
        }

        console.info('Captured event auth:forgotPassword:', data);
    });

    eventBus.on('auth:updatePassword', (data) => {
        console.info('Captured event auth:updatePassword:', data);
    });

    eventBus.on('new:sale', ({ data, session }) => {
        if(session?.user?.email === data.email_customer) {
            data.name = data.customer.split(" ")[0]
            if (data.email_customer) {
                const mailOptions: MailOptions = {
                    subject: '¡Tu orden de EBP Publicidad ha sido recibida!',
                    to: data.email_customer,
                    template: 'thank',
                    context: data
                }
                const sender = useMailSender()

                sender.send(mailOptions)
            }

            if (data.notify_email?.length > 0) {
                data.notify_email.forEach((item: any) => {
                    const mailOptions: MailOptions = {
                        subject: '¡Nueva orden en EBP Publicidad!',
                        to: item.email,
                        template: 'new_sale',
                        context: data
                    }
                    const sender = useMailSender()

                    sender.send(mailOptions)
                });
            }
        }

        console.info('Captured event new:sale:', data);
    });

    eventBus.on('error:xml', async (data) => {
        const mailOptions: MailOptions = {
            subject: 'Nuevo error de EBP Publicidad',
            to: '<EMAIL>',
            template: 'error',
            context: { error: data }
        }
        const sender = useMailSender()
        sender.send(mailOptions)

        console.info('Captured event error:', data)
    });

    eventBus.on('info:xml', async (data) => {
        const mailOptions: MailOptions = {
            subject: 'Nueva información de EBP Publicidad',
            to: '<EMAIL>',
            template: 'info',
            context: { info: data }
        }
        const sender = useMailSender()
        sender.send(mailOptions)

        console.info('Captured event info:', data);
    })
})
