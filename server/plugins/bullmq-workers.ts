export default defineNitroPlugin(async (nitroApp) => {
    console.info('\n🚀 #### INITIALIZING BULLMQ WORKERS ####')

    const config = useRuntimeConfig()
    console.info('📋 Redis Configuration:')
    console.info(`   - Host: ${config.redis?.host || 'NOT_SET'}`)
    console.info(`   - Port: ${config.redis?.port || 'NOT_SET'}`)
    console.info(`   - Password: ${config.redis?.password ? '***SET***' : 'NOT_SET'}`)
    console.info(`   - Environment: ${process.env.NODE_ENV || 'development'}`)

    try {
        // Import the workers to initialize them
        console.info('📦 Loading worker definitions...')
        await import('../queues/processor')

        console.info('✅ BullMQ Workers initialized successfully!')
        console.info('🔧 Active Workers:')
        console.info('   - all-processors-enyes worker: ACTIVE ✓')
        console.info('   - products-processing-enyes worker: ACTIVE ✓')
        console.info('==========================================\n')

        // Test Redis connection
        console.info('🔍 Testing Redis connection...')
        const { Queue } = await import('bullmq')
        const testQueue = new Queue('startup-test-queue', {
            connection: {
                host: config.redis?.host || 'localhost',
                port: config.redis?.port || 6379,
                password: config.redis?.password
            }
        })

        await testQueue.add('startup-test', { message: 'Worker startup test' })
        await testQueue.close()

        console.info('✅ Redis connection test successful!')
        console.info('==========================================\n')

    } catch (error: any) {
        console.error('\n❌ #### BULLMQ WORKERS INITIALIZATION FAILED ####')
        console.error('💥 Error:', error.message)
        console.error('📍 Stack:', error.stack)
        console.error('🔧 Troubleshooting:')
        console.error('   1. Check if Redis server is running: redis-cli ping')
        console.error('   2. Verify REDIS_HOST and REDIS_PORT environment variables')
        console.error('   3. Check Redis password if required')
        console.error('   4. Ensure Redis is accessible from this server')
        console.error('==========================================\n')
    }
})
