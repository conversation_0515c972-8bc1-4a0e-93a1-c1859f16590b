{"name": "nuxt3-primevue-boilerplate", "private": false, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev --host", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@chenfengyuan/vue-qrcode": "^2.0.0", "@dargmuesli/nuxt-cookie-control": "^9.0.6", "@nuxt/fonts": "^0.11.4", "@nuxt/icon": "^1.13.0", "@nuxt/image": "^1.10.0", "@nuxtjs/color-mode": "^3.5.2", "@nuxtjs/i18n": "^9.5.4", "@nuxtjs/seo": "^3.1.0", "@nuxtjs/tailwindcss": "^6.14.0", "@pinia/nuxt": "^0.11.1", "@primeuix/themes": "^1.1.1", "@primevue/forms": "^4.3.4", "@unhead/vue": "^2.0.10", "bcryptjs": "^3.0.2", "bullmq": "^5.56.0", "jose": "^6.0.11", "nuxt": "^3.17.4", "nuxt-arpix-db-connect": "^1.0.1", "nuxt-arpix-email-sender": "^1.0.0", "nuxt-auth-utils": "^0.5.20", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.3.0", "primevue": "^4.3.4", "qrcode": "^1.5.4", "vue": "^3.5.14", "vue-easy-lightbox": "^1.19.0", "vue-router": "^4.5.1"}, "devDependencies": {"@primevue/nuxt-module": "^4.3.4", "postcss-import": "^16.1.0"}}