# BullMQ Products Processing

Este documento explica cómo usar el sistema de colas BullMQ para el procesamiento de productos de Enyes.

## Descripción

El endpoint de productos ha sido migrado para usar BullMQ, permitiendo el procesamiento en segundo plano de grandes volúmenes de datos de productos sin bloquear la respuesta HTTP.

## Endpoints Disponibles

### 1. Procesar Productos (POST)
```
POST /api/enyes/system/products
```

**Parámetros del body (opcionales):**
```json
{
  "useQueue": true  // true = usar cola (por defecto), false = procesamiento directo
}
```

**Respuesta con cola (useQueue: true):**
```json
{
  "success": true,
  "message": "Products processing job added to queue successfully",
  "jobId": "12345",
  "status": "queued",
  "useQueue": true
}
```

**Respuesta directa (useQueue: false):**
```json
{
  "product": [...],
  "colors": [...],
  "sizes": [...],
  "variant": [...],
  "product_category": [...],
  "marking": [...],
  "success": true,
  "message": "Products processed successfully",
  "useQueue": false
}
```

### 2. Agregar a Cola (POST)
```
POST /api/enyes/system/products-queue
```

Agrega un trabajo de procesamiento de productos a la cola.

**Respuesta:**
```json
{
  "success": true,
  "message": "Products processing job added to queue successfully",
  "jobId": "12345",
  "status": "queued"
}
```

### 3. Estado de la Cola (GET)
```
GET /api/enyes/system/products-queue-status
GET /api/enyes/system/products-queue-status?jobId=12345
```

### 4. Logs del Trabajo (GET)
```
GET /api/enyes/system/products-queue-logs?jobId=12345
GET /api/enyes/system/products-queue-logs?jobId=12345&format=text
```

### 5. Trabajo de Prueba (POST)
```
POST /api/enyes/system/test-products-queue
```

### 6. Debug de Marcajes (GET)
```
GET /api/enyes/system/debug-markings?limit=10
```

### 7. Prueba de Marcajes (POST)
```
POST /api/enyes/system/test-markings
POST /api/enyes/system/test-markings -d '{"testMode": false, "markings": [...]}'
```

**Sin jobId - Estado general de la cola:**
```json
{
  "queue": "products-processing-enyes",
  "counts": {
    "waiting": 0,
    "active": 1,
    "completed": 5,
    "failed": 0
  },
  "jobs": {
    "waiting": [],
    "active": [...],
    "completed": [...],
    "failed": [...]
  }
}
```

**Con jobId - Estado específico del trabajo:**
```json
{
  "jobId": "12345",
  "state": "completed",
  "progress": 100,
  "data": {...},
  "result": {...},
  "error": null,
  "createdAt": "2024-01-01T10:00:00.000Z",
  "processedOn": "2024-01-01T10:00:05.000Z",
  "finishedOn": "2024-01-01T10:05:30.000Z"
}
```

## Estados de los Trabajos

- **waiting**: En espera de ser procesado
- **active**: Siendo procesado actualmente
- **completed**: Completado exitosamente
- **failed**: Falló durante el procesamiento
- **delayed**: Retrasado (para reintentos)

## Configuración

La configuración de BullMQ se encuentra en:
- `server/queues/processor.ts` - Definición de colas y workers
- `nuxt.config.ts` - Configuración de Redis

### Variables de Entorno Requeridas

```env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_password  # Solo en producción
```

## Ventajas del Procesamiento en Cola

1. **No Bloqueo**: Las respuestas HTTP son inmediatas
2. **Escalabilidad**: Múltiples workers pueden procesar trabajos en paralelo
3. **Confiabilidad**: Reintentos automáticos en caso de fallo
4. **Monitoreo**: Estado y progreso de los trabajos
5. **Persistencia**: Los trabajos se mantienen en Redis

## Configuración del Worker

```typescript
const productsWorker = new Worker('products-processing-enyes', async (job) => {
  // Procesamiento del trabajo
}, {
  connection: { /* Redis config */ },
  lockDuration: 300000,  // 5 minutos
  concurrency: 1,        // Un trabajo a la vez
  attempts: 3,           // 3 intentos máximo
  backoff: {
    type: 'exponential',
    delay: 30000         // 30 segundos inicial
  }
});
```

## Ejemplos de Uso

### JavaScript/Frontend
```javascript
// Usar cola (recomendado)
const response = await fetch('/api/enyes/system/products', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ useQueue: true })
});

const { jobId } = await response.json();

// Verificar estado
const statusResponse = await fetch(`/api/enyes/system/products-queue-status?jobId=${jobId}`);
const status = await statusResponse.json();
```

### cURL
```bash
# Agregar a cola
curl -X POST http://localhost:3000/api/enyes/system/products-queue

# Verificar estado
curl http://localhost:3000/api/enyes/system/products-queue-status

# Procesamiento directo
curl -X POST http://localhost:3000/api/enyes/system/products \
  -H "Content-Type: application/json" \
  -d '{"useQueue": false}'
```

## Monitoreo y Logs

### Logs Detallados del Worker

Los logs del worker aparecen en la consola del servidor con información detallada:

```
🚀 #### PRODUCTS WORKER STARTED ####
📋 Job ID: 12345
⏰ Started at: 2024-01-01 10:00:00
🔧 Config: {
  "url": "https://api.example.com",
  "userId": "user123"
}
==========================================

📊 [  0%] Iniciando procesamiento de productos... (0s elapsed)
📊 [  5%] Obteniendo lista de productos del catálogo... (1s elapsed)
📊 [ 10%] Encontrados 150 productos en el catálogo (2s elapsed)
📊 [ 15%] Iniciando procesamiento de datos de productos... (3s elapsed)
📊 [ 25%] Procesando lote 1/3 (50 productos)... (5s elapsed)
🔄 [Job 12345] 25% - Procesando lote 1/3 (50 productos)... (5s)
📊 [ 35%] Lote 1/3 completado: 48 exitosos, 2 fallidos. Total acumulado: 48 productos, 120 variantes (15s elapsed)
📊 [ 50%] Procesando lote 2/3 (50 productos)... (16s elapsed)
📊 [ 65%] Lote 2/3 completado: 50 exitosos, 0 fallidos. Total acumulado: 98 productos, 245 variantes (28s elapsed)
📊 [ 75%] Procesamiento completado. Guardando 148 productos, 25 colores, 30 tallas... (35s elapsed)
📊 [ 80%] Guardando colores, tallas y productos... (36s elapsed)
📊 [ 90%] Guardando categorías y variantes de productos... (38s elapsed)
📊 [ 95%] Guardando información de marcajes... (40s elapsed)
📊 [100%] ¡Procesamiento completado exitosamente! 148 productos, 365 variantes, 25 colores, 30 tallas (42s elapsed)

✅ #### PRODUCTS WORKER COMPLETED ####
📋 Job ID: 12345
⏱️  Total time: 42s
📊 Results:
   - Products: 148
   - Colors: 25
   - Sizes: 30
   - Variants: 365
   - Categories: 89
   - Markings: 45
⏰ Finished at: 2024-01-01 10:00:42
==========================================

🎉 #### JOB COMPLETED EVENT ####
📋 Job ID: 12345
📊 Final Results Summary:
   ✓ Products: 148
   ✓ Colors: 25
   ✓ Sizes: 30
   ✓ Variants: 365
   ✓ Categories: 89
   ✓ Markings: 45
⏰ Completed at: 2024-01-01 10:00:42
=====================================
```

### Monitoreo desde Línea de Comandos

Se incluye un script para monitorear el progreso:

```bash
# Mostrar estado de la cola
node scripts/monitor-products-queue.js status

# Iniciar trabajo de prueba y monitorearlo
node scripts/monitor-products-queue.js test

# Monitorear trabajo específico
node scripts/monitor-products-queue.js 12345
```

Salida del monitoreo en tiempo real:
```
🔍 Monitoring job: 12345
Press Ctrl+C to stop monitoring

🔄 [42s] ACTIVE - 75% - Procesamiento completado. Guardando 148 productos, 25 colores, 30 tallas... (35s)

🎉 Job completed successfully!
📊 Results:
   - Products: 148
   - Colors: 25
   - Sizes: 30
   - Variants: 365
```

## Troubleshooting

### Errores Comunes

#### 1. Redis no conecta
- Verificar variables de entorno `REDIS_HOST`, `REDIS_PORT`, `REDIS_PASSWORD`
- Confirmar que Redis esté ejecutándose: `redis-cli ping`

#### 2. Error: `error.graphQLOperationFailed` en saveMarking
Este error ocurre cuando hay problemas con los datos de marcajes:

**Diagnóstico:**
```bash
# Verificar estructura de datos
curl http://localhost:3000/api/enyes/system/debug-markings

# Probar inserción con datos de prueba
curl -X POST http://localhost:3000/api/enyes/system/test-markings
```

**Posibles causas:**
- Tabla `enyes_print` vacía o sin los IDs requeridos
- Campo `area` con estructura JSON inválida
- Violación de restricciones de clave primaria
- Permisos insuficientes en la base de datos

**Solución implementada:**
El sistema ahora continúa procesando aunque falle el guardado de marcajes:
```
📊 Results:
   - Markings: 0 (with errors)
```

#### 3. Trabajos se quedan en "active"
- Verificar `lockDuration` (actualmente 5 minutos)
- Reiniciar workers si es necesario
- Revisar logs para errores de timeout

#### 4. Trabajos fallan repetidamente
- Revisar logs detallados del worker
- Verificar configuración de la API externa
- Comprobar conectividad de red

### Herramientas de Debug

#### Monitoreo en Tiempo Real
```bash
# Monitorear trabajo específico
node scripts/monitor-products-queue.js <jobId>

# Ver estado general
node scripts/monitor-products-queue.js status

# Ejecutar prueba completa
node scripts/monitor-products-queue.js test
```

#### Logs Detallados
Los logs incluyen información específica sobre cada fase:
- Obtención de catálogo
- Procesamiento por lotes
- Guardado de datos
- Errores específicos con contexto

#### Endpoints de Debug
- `GET /api/enyes/system/debug-markings` - Analizar datos de marcajes
- `POST /api/enyes/system/test-markings` - Probar inserción de marcajes
- `GET /api/enyes/system/products-queue-logs?jobId=X` - Ver logs específicos
