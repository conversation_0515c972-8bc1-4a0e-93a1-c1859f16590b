# BullMQ Products Processing

Este documento explica cómo usar el sistema de colas BullMQ para el procesamiento de productos de Enyes.

## Descripción

El endpoint de productos ha sido migrado para usar BullMQ, permitiendo el procesamiento en segundo plano de grandes volúmenes de datos de productos sin bloquear la respuesta HTTP.

## Endpoints Disponibles

### 1. Procesar Productos (POST)
```
POST /api/enyes/system/products
```

**Parámetros del body (opcionales):**
```json
{
  "useQueue": true  // true = usar cola (por defecto), false = procesamiento directo
}
```

**Respuesta con cola (useQueue: true):**
```json
{
  "success": true,
  "message": "Products processing job added to queue successfully",
  "jobId": "12345",
  "status": "queued",
  "useQueue": true
}
```

**Respuesta directa (useQueue: false):**
```json
{
  "product": [...],
  "colors": [...],
  "sizes": [...],
  "variant": [...],
  "product_category": [...],
  "marking": [...],
  "success": true,
  "message": "Products processed successfully",
  "useQueue": false
}
```

### 2. Agregar a Cola (POST)
```
POST /api/enyes/system/products-queue
```

Agrega un trabajo de procesamiento de productos a la cola.

**Respuesta:**
```json
{
  "success": true,
  "message": "Products processing job added to queue successfully",
  "jobId": "12345",
  "status": "queued"
}
```

### 3. Estado de la Cola (GET)
```
GET /api/enyes/system/products-queue-status
GET /api/enyes/system/products-queue-status?jobId=12345
```

**Sin jobId - Estado general de la cola:**
```json
{
  "queue": "products-processing-enyes",
  "counts": {
    "waiting": 0,
    "active": 1,
    "completed": 5,
    "failed": 0
  },
  "jobs": {
    "waiting": [],
    "active": [...],
    "completed": [...],
    "failed": [...]
  }
}
```

**Con jobId - Estado específico del trabajo:**
```json
{
  "jobId": "12345",
  "state": "completed",
  "progress": 100,
  "data": {...},
  "result": {...},
  "error": null,
  "createdAt": "2024-01-01T10:00:00.000Z",
  "processedOn": "2024-01-01T10:00:05.000Z",
  "finishedOn": "2024-01-01T10:05:30.000Z"
}
```

## Estados de los Trabajos

- **waiting**: En espera de ser procesado
- **active**: Siendo procesado actualmente
- **completed**: Completado exitosamente
- **failed**: Falló durante el procesamiento
- **delayed**: Retrasado (para reintentos)

## Configuración

La configuración de BullMQ se encuentra en:
- `server/queues/processor.ts` - Definición de colas y workers
- `nuxt.config.ts` - Configuración de Redis

### Variables de Entorno Requeridas

```env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_password  # Solo en producción
```

## Ventajas del Procesamiento en Cola

1. **No Bloqueo**: Las respuestas HTTP son inmediatas
2. **Escalabilidad**: Múltiples workers pueden procesar trabajos en paralelo
3. **Confiabilidad**: Reintentos automáticos en caso de fallo
4. **Monitoreo**: Estado y progreso de los trabajos
5. **Persistencia**: Los trabajos se mantienen en Redis

## Configuración del Worker

```typescript
const productsWorker = new Worker('products-processing-enyes', async (job) => {
  // Procesamiento del trabajo
}, {
  connection: { /* Redis config */ },
  lockDuration: 300000,  // 5 minutos
  concurrency: 1,        // Un trabajo a la vez
  attempts: 3,           // 3 intentos máximo
  backoff: {
    type: 'exponential',
    delay: 30000         // 30 segundos inicial
  }
});
```

## Ejemplos de Uso

### JavaScript/Frontend
```javascript
// Usar cola (recomendado)
const response = await fetch('/api/enyes/system/products', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ useQueue: true })
});

const { jobId } = await response.json();

// Verificar estado
const statusResponse = await fetch(`/api/enyes/system/products-queue-status?jobId=${jobId}`);
const status = await statusResponse.json();
```

### cURL
```bash
# Agregar a cola
curl -X POST http://localhost:3000/api/enyes/system/products-queue

# Verificar estado
curl http://localhost:3000/api/enyes/system/products-queue-status

# Procesamiento directo
curl -X POST http://localhost:3000/api/enyes/system/products \
  -H "Content-Type: application/json" \
  -d '{"useQueue": false}'
```

## Monitoreo y Logs

Los logs del worker aparecen en la consola del servidor:
```
#### Worker products-processing-enyes started. Job ID: 12345 ####
Products processing job 12345 progress: 10
Products processing job 12345 progress: 100
#### Worker products-processing-enyes finished. Job ID: 12345 ####
Products processing job 12345 completed successfully.
Processed: 150 products, 25 colors, 30 sizes
```

## Troubleshooting

1. **Redis no conecta**: Verificar variables de entorno y que Redis esté ejecutándose
2. **Trabajos fallan**: Revisar logs del worker y configuración de la API externa
3. **Trabajos se quedan en "active"**: Verificar `lockDuration` y reiniciar workers si es necesario
